import sqlite3
from PyQt5.QtCore import QMutex, QMutexLocker

class DatabaseHandler:
    _instance = None
    _mutex = QMutex()
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.init_db()
        return cls._instance
    
    def init_db(self):
        self.conn = sqlite3.connect('facebook_scheduler.db', check_same_thread=False)
        self.cursor = self.conn.cursor()
        self.create_tables()
        
    def create_tables(self):
        # Table creation logic from original code
        self.cursor.execute('''CREATE TABLE IF NOT EXISTS accounts (...)''')
        self.cursor.execute('''CREATE TABLE IF NOT EXISTS posts (...)''')
        # ... other table creations
        self.conn.commit()
    
    # Add all database operations as methods
    def get_posts(self, post_type):
        with QMutexLocker(self._mutex):
            return self.cursor.execute(
                "SELECT * FROM posts WHERE post_type=?", 
                (post_type,)
            ).fetchall()
    
    # Add other CRUD operations... 