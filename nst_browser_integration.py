#!/usr/bin/env python3
"""
NST Browser Integration Script for FB Scheduler
This script provides utilities for working with NST Browser profiles
and integrating them with the FB Scheduler application.
"""

import os
import sys
import json
import time
import re
import logging
import requests
from urllib.parse import urlencode, quote
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - [%(levelname)s] - %(message)s"
)

class NSTBrowserManager:
    """Manager class for NST Browser operations."""
    
    def __init__(self, api_key='fc63ee6b-0785-4b2a-a179-d6ae22c88479', host='127.0.0.1', port=8848):
        self.api_key = api_key
        self.host = host
        self.port = port
        self.base_url = f'http://{host}:{port}'
    
    def get_remote_chrome_version(self, debugger_address):
        """Query the remote debugging endpoint to retrieve the actual Chrome version."""
        try:
            url = f"http://{debugger_address}/json/version"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()
            browser_info = data.get("Browser", "")
            version_match = re.search(r'Chrome/(\d+\.\d+\.\d+\.\d+)', browser_info)
            if version_match:
                version = version_match.group(1)
                logging.info(f"Detected remote Chrome version: {version}")
                return version
            else:
                logging.error("Could not extract Chrome version from remote debugger info.")
        except Exception as e:
            logging.error(f"Error retrieving remote Chrome version: {e}")
        return None

    def get_debugger_port(self, url, max_retries=10, retry_interval=5):
        """Retrieve the debugger port by polling the specified URL."""
        for attempt in range(max_retries):
            try:
                response = requests.get(url, timeout=10)
                response.raise_for_status()
                data = response.json()
                return data.get('data', {}).get('port')
            except requests.RequestException as e:
                logging.warning(f"Error retrieving debugger port: {e}. Retrying...")
                time.sleep(retry_interval)
        logging.critical("Failed to retrieve debugger port.")
        return None

    def launch_and_connect_to_browser(self, profile_id, max_retries=10, retry_interval=5):
        """Launch browser for the given profile and retrieve debugger address."""
        config = {'headless': False, 'autoClose': True}
        query = urlencode({'x-api-key': self.api_key, 'config': quote(json.dumps(config))})
        url = f'{self.base_url}/devtool/launch/{profile_id}?{query}'
        
        port = self.get_debugger_port(url, max_retries, retry_interval)
        if port:
            return f"{self.host}:{port}"
        return None

    def kill_nstchrome_processes(self):
        """Kill nstchrome.exe processes."""
        try:
            os.system("taskkill /F /IM nstchrome.exe > NUL 2>&1")
            logging.info("Killed nstchrome.exe processes")
        except Exception as e:
            logging.error(f"Error killing nstchrome processes: {e}")

    def connect_selenium(self, debugger_address):
        """Start Selenium WebDriver using the debugger address."""
        try:
            options = Options()
            options.debugger_address = debugger_address

            # Try to get the remote Chrome version for better compatibility
            remote_version = self.get_remote_chrome_version(debugger_address)
            if remote_version:
                service = Service(ChromeDriverManager(driver_version=remote_version).install())
            else:
                service = Service(ChromeDriverManager().install())

            driver = webdriver.Chrome(service=service, options=options)
            logging.info(f"Connected to NST Browser at {debugger_address}")
            return driver
        except Exception as e:
            logging.error(f"Failed to initialize WebDriver: {e}")
            return None

    def load_profile_ids(self, file_path):
        """Load profile IDs from file."""
        try:
            with open(file_path, 'r') as file:
                return [line.strip() for line in file if line.strip()]
        except Exception as e:
            logging.error(f"Error loading profiles: {e}")
            return []

    def test_profile_connection(self, profile_id):
        """Test connection to a specific profile."""
        try:
            self.kill_nstchrome_processes()
            time.sleep(2)
            
            logging.info(f"Testing profile: {profile_id}")
            debugger_address = self.launch_and_connect_to_browser(profile_id)
            
            if not debugger_address:
                logging.error(f"Failed to launch browser for profile {profile_id}")
                return False
            
            driver = self.connect_selenium(debugger_address)
            if not driver:
                logging.error(f"Failed to connect WebDriver for profile {profile_id}")
                return False
            
            try:
                driver.get("https://www.facebook.com")
                time.sleep(5)
                
                title = driver.title
                current_url = driver.current_url
                
                logging.info(f"Profile {profile_id} test successful:")
                logging.info(f"  Title: {title}")
                logging.info(f"  URL: {current_url}")
                
                return True
                
            except Exception as e:
                logging.error(f"Error testing profile {profile_id}: {e}")
                return False
            finally:
                try:
                    driver.quit()
                except:
                    pass
                    
        except Exception as e:
            logging.error(f"Error testing profile {profile_id}: {e}")
            return False

    def process_profiles_interactive(self, profile_file):
        """Process profiles interactively."""
        profiles = self.load_profile_ids(profile_file)
        
        if not profiles:
            logging.info("No profiles to process.")
            return
        
        for profile_id in profiles:
            self.kill_nstchrome_processes()
            time.sleep(2)
            
            logging.info(f"Processing profile: {profile_id}")
            debugger_address = self.launch_and_connect_to_browser(profile_id)
            
            if not debugger_address:
                logging.error(f"Failed to launch browser for profile {profile_id}")
                continue
            
            driver = self.connect_selenium(debugger_address)
            if not driver:
                logging.error(f"Failed to connect WebDriver for profile {profile_id}")
                continue
            
            try:
                driver.get("https://www.facebook.com")
                time.sleep(5)
                
                logging.info(f"Profile {profile_id} loaded Facebook successfully.")
                logging.info("Browser will remain open for manual interaction.")
                
                input(f"Press Enter to continue to next profile or close browser for {profile_id}...")
                
            except Exception as e:
                logging.error(f"Error processing profile {profile_id}: {e}")
            
            # Note: We don't close the driver here to allow manual interaction
            
        logging.info("Completed processing all profiles.")

def main():
    """Main function for command line usage."""
    if len(sys.argv) < 2:
        print("Usage: python nst_browser_integration.py <command> [args]")
        print("Commands:")
        print("  test <profile_id>           - Test connection to a specific profile")
        print("  process <profile_file>      - Process profiles from file interactively")
        print("  list-profiles <file>        - List profiles from file")
        return
    
    command = sys.argv[1]
    manager = NSTBrowserManager()
    
    if command == "test" and len(sys.argv) >= 3:
        profile_id = sys.argv[2]
        success = manager.test_profile_connection(profile_id)
        print(f"Profile {profile_id} test: {'SUCCESS' if success else 'FAILED'}")
        
    elif command == "process" and len(sys.argv) >= 3:
        profile_file = sys.argv[2]
        manager.process_profiles_interactive(profile_file)
        
    elif command == "list-profiles" and len(sys.argv) >= 3:
        profile_file = sys.argv[2]
        profiles = manager.load_profile_ids(profile_file)
        print(f"Found {len(profiles)} profiles:")
        for i, profile_id in enumerate(profiles, 1):
            print(f"  {i}. {profile_id}")
    else:
        print("Invalid command or missing arguments.")

if __name__ == "__main__":
    main()
