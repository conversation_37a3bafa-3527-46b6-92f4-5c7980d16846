from PyQt5.QtWidgets import QMainWindow
from .widgets.posts_table import PostsTable
from .widgets.schedule_calendar import ScheduleCalendar

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        # Initialize main components
        self.posts_table = PostsTable()
        self.schedule_calendar = ScheduleCalendar()
        # ... other UI setup
        
    def setup_connections(self):
        # Connect signals and slots
        self.posts_table.post_selected.connect(self.handle_post_selection)
        # ... other connections
        
    # Add main window methods... 