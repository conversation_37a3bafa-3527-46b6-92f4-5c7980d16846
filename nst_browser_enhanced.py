#!/usr/bin/env python3
"""
Enhanced NST Browser Integration Script
Based on the improved version with better Chrome version detection and WebDriver management.
"""

import logging
import os
import json
import time
import re
import subprocess
import requests
from urllib.parse import quote, urlencode, urlparse
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium import webdriver
from webdriver_manager.chrome import ChromeDriverManager

# Suppress Selenium logs
selenium_logger = logging.getLogger('selenium.webdriver')
selenium_logger.setLevel(logging.ERROR)

logging.basicConfig(level=logging.INFO, format="%(asctime)s - [%(levelname)s] - %(message)s")

profile_file_path = 'profile_ids.txt'

# Create the profile file if it does not exist
if not os.path.exists(profile_file_path):
    open(profile_file_path, 'w').close()

def get_remote_chrome_version(debugger_address):
    """Query the remote debugging endpoint to retrieve the actual Chrome version."""
    try:
        url = f"http://{debugger_address}/json/version"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        browser_info = data.get("Browser", "")
        version_match = re.search(r'Chrome/(\d+\.\d+\.\d+\.\d+)', browser_info)
        if version_match:
            version = version_match.group(1)
            logging.info(f"Detected remote Chrome version: {version}")
            return version
        else:
            logging.error("Could not extract Chrome version from remote debugger info.")
    except Exception as e:
        logging.error(f"Error retrieving remote Chrome version: {e}")
    return None

def get_debugger_port(url: str, max_retries: int = 10, retry_interval: int = 5):
    """Retrieve the debugger port by polling the specified URL."""
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()
            return data.get('data', {}).get('port')
        except requests.RequestException as e:
            logging.warning(f"Error retrieving debugger port: {e}. Retrying...")
            time.sleep(retry_interval)
    logging.critical("Failed to retrieve debugger port.")
    return None

def exec_selenium(debugger_address):
    """Start Selenium WebDriver using the debugger address."""
    try:
        options = Options()
        options.debugger_address = debugger_address
        remote_version = get_remote_chrome_version(debugger_address)
        if remote_version:
            service = Service(ChromeDriverManager(driver_version=remote_version).install())
        else:
            service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        return driver
    except Exception as e:
        logging.error(f"Failed to initialize WebDriver: {e}")
        return None

def launch_and_connect_to_browser(profile_id, max_retries=10, retry_interval=5):
    """Launch browser for the given profile and retrieve debugger address."""
    api_key = 'fc63ee6b-0785-4b2a-a179-d6ae22c88479'
    host = '127.0.0.1'
    config = {'headless': False, 'autoClose': True}
    query = urlencode({'x-api-key': api_key, 'config': quote(json.dumps(config))})
    url = f'http://{host}:8848/devtool/launch/{profile_id}?{query}'
    
    port = get_debugger_port(url, max_retries, retry_interval)
    if port:
        return f"{host}:{port}"
    return None

def kill_nstchrome_processes():
    """Kill nstchrome.exe processes."""
    try:
        os.system("taskkill /F /IM nstchrome.exe > NUL 2>&1")
        logging.info("Killed nstchrome.exe processes")
    except Exception as e:
        logging.error(f"Error killing nstchrome processes: {e}")

def load_profile_ids(file_path):
    """Load profile IDs from file."""
    try:
        with open(file_path, 'r') as file:
            return [line.strip() for line in file if line.strip()]
    except Exception as e:
        logging.error(f"Error loading profiles: {e}")
        return []

def test_profile_connection(profile_id):
    """Test connection to a specific profile."""
    try:
        kill_nstchrome_processes()
        time.sleep(2)
        
        logging.info(f"Testing profile: {profile_id}")
        debugger_address = launch_and_connect_to_browser(profile_id)
        
        if not debugger_address:
            logging.error(f"Failed to launch browser for profile {profile_id}")
            return False
        
        driver = exec_selenium(debugger_address)
        if not driver:
            logging.error(f"Failed to connect WebDriver for profile {profile_id}")
            return False
        
        try:
            driver.get("https://www.facebook.com")
            time.sleep(5)
            
            title = driver.title
            current_url = driver.current_url
            
            logging.info(f"Profile {profile_id} test successful:")
            logging.info(f"  Title: {title}")
            logging.info(f"  URL: {current_url}")
            
            return True
            
        except Exception as e:
            logging.error(f"Error testing profile {profile_id}: {e}")
            return False
        finally:
            try:
                driver.quit()
            except:
                pass
                
    except Exception as e:
        logging.error(f"Error testing profile {profile_id}: {e}")
        return False

def process_profiles(profile_file):
    """Process profiles using WebDriver."""
    processed_profiles = set()
    profiles = load_profile_ids(profile_file)

    if not profiles:
        logging.info("No profiles to process.")
        return

    for profile_id in profiles:
        if profile_id in processed_profiles:
            continue

        kill_nstchrome_processes()
        logging.info(f"Processing profile: {profile_id}")
        debugger_address = launch_and_connect_to_browser(profile_id)

        if not debugger_address:
            logging.error(f"Failed to launch browser for profile {profile_id}")
            continue

        driver = exec_selenium(debugger_address)
        if not driver:
            logging.error(f"Failed to connect WebDriver for profile {profile_id}")
            continue

        try:
            target_url = "https://www.facebook.com"
            driver.get(target_url)
            time.sleep(5)  # Wait for the page to load
            logging.info(f"Profile {profile_id} should now have Facebook loaded. Perform login if necessary.")
            logging.info("Browser will remain open. End the session manually when ready.")
            input("Press Enter to end this session and close the browser...")
        except Exception as e:
            logging.error(f"Error processing profile {profile_id}: {e}")
        # Do not close the browser automatically; allow manual termination.
        processed_profiles.add(profile_id)
    logging.info("Completed processing all profiles.")

def main():
    """Main function for command line usage."""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python nst_browser_enhanced.py <command> [args]")
        print("Commands:")
        print("  test <profile_id>           - Test connection to a specific profile")
        print("  process <profile_file>      - Process profiles from file interactively")
        print("  list-profiles <file>        - List profiles from file")
        print("  process-default             - Process profiles from profile_ids.txt")
        return
    
    command = sys.argv[1]
    
    if command == "test" and len(sys.argv) >= 3:
        profile_id = sys.argv[2]
        success = test_profile_connection(profile_id)
        print(f"Profile {profile_id} test: {'SUCCESS' if success else 'FAILED'}")
        
    elif command == "process" and len(sys.argv) >= 3:
        profile_file = sys.argv[2]
        process_profiles(profile_file)
        
    elif command == "process-default":
        process_profiles(profile_file_path)
        
    elif command == "list-profiles" and len(sys.argv) >= 3:
        profile_file = sys.argv[2]
        profiles = load_profile_ids(profile_file)
        print(f"Found {len(profiles)} profiles:")
        for i, profile_id in enumerate(profiles, 1):
            print(f"  {i}. {profile_id}")
    else:
        print("Invalid command or missing arguments.")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        # Default behavior: process profiles interactively
        logging.info("Starting profile processing...")
        process_profiles(profile_file_path)
    else:
        main()
