import os
import sys
import csv
import sqlite3
import datetime
import shutil
import logging
import json
import random
import glob
import subprocess
import time  # Added for delays
from functools import partial
from dataclasses import dataclass, field # For data structures
from urllib.parse import urlencode, quote
import requests

# --- Configuration & Constants ---
DB_FILE = 'facebook_scheduler.db'
LOG_FILE = 'error.log'
PID_OFFSET = 8309471 # Define as a constant
DEFAULT_THUMBNAIL_SIZE = (100, 100)
SMALL_THUMBNAIL_SIZE = (30, 30)
POST_CATEGORIES = ["Recipes", "Engage", "Parole"] # Use constant

# --- Logging Configuration ---
# (Keep your existing logging setup - it's good)
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - [%(levelname)s] - %(threadName)s - %(message)s", # Added threadName
    filename=LOG_FILE,
    filemode="a"
)
console = logging.StreamHandler(sys.stdout)
console.setLevel(logging.INFO) # Maybe set console to INFO for less noise
formatter = logging.Formatter("%(asctime)s - [%(levelname)s] - %(message)s")
console.setFormatter(formatter)
logging.getLogger().addHandler(console)
logging.info("Application starting...")

# --- Third-Party Imports ---
try:
    import pandas as pd
    from openpyxl import load_workbook
    from PIL import Image
    import chromedriver_autoinstaller
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    import undetected_chromedriver as uc # Using this based on your login code

    from PyQt5 import QtCore, QtGui, QtWidgets
    from PyQt5.QtCore import (Qt, QTimer, QThread, pyqtSignal, QMutex, QMutexLocker,
                            QUrl, QStandardPaths, QSize, QPoint)
    from PyQt5.QtGui import QPixmap, QColor, QIcon, QCursor, QDesktopServices, QPainter, QBrush # Added QPainter and QBrush
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QWidget, QSplitter, QStackedWidget, QPushButton,
        QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTextEdit, QComboBox, QFileDialog,
        QMessageBox, QCalendarWidget, QDialog, QFormLayout, QInputDialog, QSizePolicy,
        QTableWidget, QTableWidgetItem, QTabWidget, QHeaderView, QTimeEdit, QSpinBox,
        QFrame, QScrollArea, QCheckBox, QStyleOptionButton, QAbstractItemView, QListWidget,
        QGridLayout, QRadioButton, QButtonGroup, QMenu, QAction, QProgressDialog, # Added QProgressDialog
        QStatusBar, QProgressBar, QListWidgetItem, QGroupBox # Added StatusBar, ProgressBar, QListWidgetItem and QGroupBox
    )
    from PyQt5.QtChart import QChart, QChartView, QPieSeries
except ImportError as e:
    logging.critical(f"Failed to import necessary libraries: {e}. Please install requirements.")
    sys.exit(f"Import Error: {e}. Please ensure all dependencies (pandas, openpyxl, Pillow, selenium, undetected-chromedriver, PyQt5, PyQtChart) are installed.")

# --- Global Database Setup ---
db_mutex = QMutex()

def initialize_database():
    """Initializes the database and returns connection/cursor."""
    try:
        # Ensure thread safety for the connection itself if multiple threads might access it heavily,
        # though typically operations are protected by the mutex. check_same_thread=False is needed for QThread access.
        conn = sqlite3.connect(DB_FILE, check_same_thread=False)
        conn.row_factory = sqlite3.Row # Use Row factory for dict-like access
        c = conn.cursor()
        # Use PRAGMA for potential performance improvement and WAL mode
        c.execute("PRAGMA journal_mode=WAL;")
        c.execute("PRAGMA foreign_keys = ON;") # Enforce foreign key constraints if using them

        # Create tables if they don't exist
        c.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                fb_id TEXT UNIQUE NOT NULL, -- Made fb_id unique and not null
                full_name TEXT,
                cookie TEXT,
                status TEXT DEFAULT 'live', -- Added status with default
                nst_profile_id TEXT, -- NST Browser profile ID
                browser_type TEXT DEFAULT 'chrome' -- 'chrome' or 'nst'
            );
        ''')
        # Add index on fb_id for faster lookups
        c.execute("CREATE INDEX IF NOT EXISTS idx_accounts_fb_id ON accounts (fb_id);")

        # Add new columns for NST Browser support if they don't exist
        try:
            c.execute("ALTER TABLE accounts ADD COLUMN nst_profile_id TEXT")
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            c.execute("ALTER TABLE accounts ADD COLUMN browser_type TEXT DEFAULT 'chrome'")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Add new columns for scheduled posts targeting
        try:
            c.execute("ALTER TABLE posts ADD COLUMN target_page_id INTEGER")
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            c.execute("ALTER TABLE posts ADD COLUMN target_group_id INTEGER")
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            c.execute("ALTER TABLE posts ADD COLUMN original_post_id INTEGER")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Add columns for linking posts to pages/groups
        try:
            c.execute("ALTER TABLE posts ADD COLUMN target_page_id INTEGER")
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            c.execute("ALTER TABLE posts ADD COLUMN target_group_id INTEGER")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Create pages table for extracted Facebook pages
        c.execute('''
            CREATE TABLE IF NOT EXISTS pages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                page_id TEXT,
                url TEXT,
                source TEXT DEFAULT 'manual',
                account_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(account_id) REFERENCES accounts(id) ON DELETE CASCADE
            );
        ''')

        # Create groups table for extracted Facebook groups
        c.execute('''
            CREATE TABLE IF NOT EXISTS groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                group_id TEXT,
                url TEXT,
                source TEXT DEFAULT 'manual',
                account_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(account_id) REFERENCES accounts(id) ON DELETE CASCADE
            );
        ''')

        # Add account_id column to existing pages and groups tables if they don't exist
        try:
            c.execute("ALTER TABLE pages ADD COLUMN account_id INTEGER")
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            c.execute("ALTER TABLE groups ADD COLUMN account_id INTEGER")
        except sqlite3.OperationalError:
            pass  # Column already exists

        c.execute('''
            CREATE TABLE IF NOT EXISTS posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                content TEXT,
                website_link TEXT,
                image_path TEXT,
                scheduled_time DATETIME,
                account_id INTEGER, -- Consider making this a foreign key to accounts(id)
                status TEXT DEFAULT 'pending', -- Added default
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                post_type TEXT,
                recipe_text TEXT,
                sched_status_check INTEGER DEFAULT 0, -- Consider renaming/rethinking these flags
                sched_comment_check INTEGER DEFAULT 0,
                sched_checked_check INTEGER DEFAULT 0
                -- FOREIGN KEY(account_id) REFERENCES accounts(id) ON DELETE SET NULL -- Example FK
            );
        ''')
        # Add indexes for common query columns
        c.execute("CREATE INDEX IF NOT EXISTS idx_posts_status_time ON posts (status, scheduled_time);")
        c.execute("CREATE INDEX IF NOT EXISTS idx_posts_type ON posts (post_type);")

        c.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL -- Made name unique and not null
            );
        ''')
        c.execute("CREATE INDEX IF NOT EXISTS idx_categories_name ON categories (name);")

        c.execute('''
            CREATE TABLE IF NOT EXISTS schedules (
               id INTEGER PRIMARY KEY AUTOINCREMENT,
               schedule_json TEXT NOT NULL, -- Made json not null
               created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
        ''')

        # Create activity log table for tracking operations
        c.execute('''
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                details TEXT,
                post_id INTEGER,
                account_id INTEGER,
                status TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            );
        ''')

        conn.commit()
        logging.info("Database initialized successfully.")
        return conn, c
    except sqlite3.Error as e:
        logging.critical(f"Database initialization failed: {e}")
        # Show critical error to user
        # Cannot use QMessageBox here as QApplication might not be running yet
        print(f"CRITICAL: Database error - {e}. Application cannot start.", file=sys.stderr)
        sys.exit(1)

# Initialize globals (consider encapsulating in a class/context later if it grows)
conn, c = initialize_database()

# --- Data Classes ---
@dataclass
class PostData:
    id: int = -1
    content: str = ""
    website_link: str | None = None
    image_path: str | None = None # Store as comma-separated string or JSON list? JSON might be better.
    scheduled_time: datetime.datetime | None = None
    account_id: int | None = None
    status: str = "pending"
    created_at: datetime.datetime = field(default_factory=datetime.datetime.now)
    post_type: str = ""
    recipe_text: str | None = None
    # Remove sched_* flags unless their purpose is clear and actively used

    @property
    def display_pid(self) -> str:
        return f"PID{self.id + PID_OFFSET}" if self.id != -1 else "N/A"

    @property
    def image_list(self) -> list[str]:
        if self.image_path:
            return [img.strip() for img in self.image_path.split(',') if img.strip() and os.path.exists(img.strip())]
        return []

@dataclass
class AccountData:
    id: int = -1
    fb_id: str = ""
    full_name: str | None = None
    cookie: str | None = None
    status: str = "live"
    nst_profile_id: str | None = None
    browser_type: str = "chrome"


# --- Utility Functions ---
def clearLayout(layout):
    """Removes all widgets from a layout."""
    if layout is not None:
        while layout.count():
            item = layout.takeAt(0)
            widget = item.widget()
            if widget:
                # Use deleteLater to ensure safe deletion
                widget.deleteLater()
            else:
                # Recursively clear nested layouts
                clearLayout(item.layout())

def get_web_options(headless=True):
    """Configures Chrome options."""
    options = webdriver.ChromeOptions()
    if headless:
        options.add_argument("--headless=new") # Updated headless argument
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox") # Often needed in Linux/Docker
    options.add_argument("--disable-dev-shm-usage") # Overcomes limited resource problems
    options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36") # Common user agent
    options.add_experimental_option('excludeSwitches', ['enable-logging']) # Reduce console noise
    return options

# Removed generate_hour_options (can be done inline or in specific widget)

def create_thumbnail(path, size=DEFAULT_THUMBNAIL_SIZE):
    """Creates a thumbnail QPixmap from an image path."""
    if not path or not os.path.exists(path):
        logging.warning(f"Thumbnail creation skipped: Path invalid or does not exist: {path}")
        return QPixmap() # Return empty QPixmap
    try:
        img = Image.open(path)
        img.thumbnail(size) # PIL thumbnail resizes in place
        # Convert PIL Image to QPixmap
        if img.mode == "RGB":
            r, g, b = img.split()
            img = Image.merge("RGB", (r, g, b))
        elif img.mode == "RGBA":
            r, g, b, a = img.split()
            img = Image.merge("RGBA", (r, g, b, a))
        elif img.mode == "L": # Grayscale
             img = img.convert("RGBA") # Convert to RGBA for QPixmap

        # Check conversion result
        if img.mode not in ("RGB", "RGBA"):
             logging.warning(f"Unsupported image mode '{img.mode}' for QPixmap conversion: {path}. Trying RGB conversion.")
             img = img.convert("RGB")

        if img.mode == "RGB":
             qimage = QtGui.QImage(img.tobytes("raw", "RGB"), img.width, img.height, QtGui.QImage.Format_RGB888)
        elif img.mode == "RGBA":
             qimage = QtGui.QImage(img.tobytes("raw", "RGBA"), img.width, img.height, QtGui.QImage.Format_RGBA8888)
        else: # Should not happen after conversions above
             logging.error(f"Cannot convert image mode {img.mode} to QPixmap: {path}")
             return QPixmap()

        return QPixmap.fromImage(qimage)
    except Exception as e:
        logging.error(f"Error creating thumbnail for {path}: {e}")
        return QPixmap() # Return empty QPixmap on error


def save_cookie_to_file(cookie_data, filename="account_cookie.json"):
    """Saves cookie data (dict) to a JSON file."""
    try:
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(cookie_data, f, indent=2)
        logging.info(f"Cookie data saved to {filename}")
    except IOError as e:
        logging.error(f"Error saving cookie data to {filename}: {e}")
    except Exception as e:
        logging.error(f"Unexpected error saving cookie data: {e}")


def get_global_categories() -> list[str]:
    """Fetches predefined and database categories."""
    db_cats = []
    try:
        with QMutexLocker(db_mutex):
            # Use fetchall() correctly
            rows = c.execute("SELECT name FROM categories ORDER BY name").fetchall()
        db_cats = [row['name'] for row in rows] # Access by column name due to row_factory
    except sqlite3.Error as e:
        logging.error(f"Failed to fetch categories from database: {e}")
    # Combine and remove duplicates, preserving order
    all_cats = list(dict.fromkeys(POST_CATEGORIES + db_cats))
    return all_cats

# --- Selenium Helper Functions (Consider moving to a separate module/class) ---

def selenium_task_runner(target_func, *args, **kwargs):
    """Runs a Selenium task, handles driver setup/teardown."""
    driver = None
    result = None
    error = None
    try:
        logging.debug(f"Starting Selenium task: {target_func.__name__}")
        # Consider using undetected_chromedriver if needed based on your login method
        driver_path = chromedriver_autoinstaller.install()
        service = Service(driver_path)
        options = get_web_options(headless=True) # Use headless by default
        driver = webdriver.Chrome(service=service, options=options)
        driver.implicitly_wait(5) # Small implicit wait

        result = target_func(driver, *args, **kwargs)
        logging.debug(f"Selenium task {target_func.__name__} completed.")

    except Exception as e:
        error = e
        logging.error(f"Error during Selenium task {target_func.__name__}: {e}", exc_info=True)
    finally:
        if driver:
            try:
                driver.quit()
                logging.debug("Selenium driver quit.")
            except Exception as e:
                logging.error(f"Error quitting Selenium driver: {e}")
    return result, error

def _extract_full_name_selenium(driver):
    """Internal helper to extract name within a Selenium session."""
    # Wait for page to load
    time.sleep(3)

    # Method 1: Look for profile name in various selectors
    selectors = [
        "//h1[contains(@class, 'x1heor9g')]",  # New Facebook layout
        "//h1",  # Generic H1
        "//span[contains(@class, 'x1heor9g')]",  # Profile name span
        "//div[contains(@class, 'x1e56ztr')]//span",  # Profile header
        "//div[@data-pagelet='ProfileTilesFeed']//h1",  # Profile tiles
    ]

    for selector in selectors:
        try:
            name_element = WebDriverWait(driver, 3).until(
                EC.presence_of_element_located((By.XPATH, selector))
            )
            name = name_element.text.strip()
            if name and len(name) > 1 and not name.lower() in ['facebook', 'home', 'profile']:
                logging.info(f"Extracted name using selector {selector}: {name}")
                return name
        except Exception:
            continue

    # Method 2: Meta tag og:title
    try:
        meta = driver.find_element(By.XPATH, "//meta[@property='og:title']")
        name = meta.get_attribute("content").strip()
        if name and len(name) > 1:
            # Clean up common Facebook suffixes
            name = name.replace(" | Facebook", "").replace(" - Facebook", "").strip()
            if name and not name.lower() in ['facebook', 'home']:
                logging.info(f"Extracted name using og:title meta tag: {name}")
                return name
    except Exception:
        pass

    # Method 3: Page title
    try:
        title = driver.title.strip()
        if title:
            # Clean up title
            name = title.split("|")[0].split("-")[0].strip()
            name = name.replace("Facebook", "").replace("(", "").replace(")", "").strip()
            if name and len(name) > 1:
                logging.info(f"Extracted name using page title: {name}")
                return name
    except Exception:
        pass

    # Method 4: Look in page source for name patterns
    try:
        page_source = driver.page_source
        import re
        patterns = [
            r'"name":"([^"]+)"',
            r'"displayName":"([^"]+)"',
            r'"title":"([^"]+)"'
        ]

        for pattern in patterns:
            match = re.search(pattern, page_source)
            if match:
                name = match.group(1).strip()
                if name and len(name) > 1 and not name.lower() in ['facebook', 'home', 'profile']:
                    logging.info(f"Extracted name via pattern {pattern}: {name}")
                    return name
    except Exception:
        pass

    logging.warning("Could not extract full name using any method.")
    return "Unknown Name"

def _extract_fb_uid_selenium(driver):
    """Internal helper to extract UID within a Selenium session."""
    import re

    # Wait for page to load
    time.sleep(3)

    # Method 1: Try common meta tag first
    try:
        meta = driver.find_element(By.XPATH, "//meta[@property='al:ios:url']")
        url = meta.get_attribute("content")
        # Expected format like: "fb://profile/1000..." or "fb://page/?id=123..."
        if url and "profile/" in url:
            uid = url.split("profile/")[-1].split("?")[0]
            if uid.isdigit() and len(uid) > 5:
                logging.info(f"Extracted UID via al:ios:url (profile): {uid}")
                return uid
        elif url and "page/?id=" in url:
             uid = url.split("page/?id=")[-1].split("&")[0]
             if uid.isdigit() and len(uid) > 5:
                 logging.info(f"Extracted UID via al:ios:url (page): {uid}")
                 return uid
    except Exception as e:
        logging.debug(f"Could not extract UID via al:ios:url meta tag: {e}")

    # Method 2: Look for profile link with ID in href
    try:
        link = driver.find_element(By.XPATH, "//a[contains(@href,'profile.php?id=')]")
        href = link.get_attribute("href")
        uid = href.split("id=")[-1].split("&")[0]
        if uid.isdigit() and len(uid) > 5:
            logging.info(f"Extracted UID via profile.php link: {uid}")
            return uid
    except Exception as e:
        logging.debug(f"Could not extract UID via profile.php link: {e}")

    # Method 3: Entity ID from page source
    try:
        page_source = driver.page_source
        match = re.search(r'"entity_id":"(\d+)"', page_source)
        if match:
            uid = match.group(1)
            if uid.isdigit() and len(uid) > 5:
                logging.info(f"Extracted UID via entity_id in page source: {uid}")
                return uid
    except Exception as e:
        logging.debug(f"Could not extract UID via entity_id: {e}")

    # Method 4: Look for user ID in various page source patterns
    try:
        page_source = driver.page_source
        patterns = [
            r'"USER_ID":"(\d+)"',
            r'"userID":"(\d+)"',
            r'"actorID":"(\d+)"',
            r'"pageID":"(\d+)"',
            r'c_user=(\d+)',
            r'"id":(\d+),"name"'
        ]

        for pattern in patterns:
            match = re.search(pattern, page_source)
            if match:
                uid = match.group(1)
                if uid.isdigit() and len(uid) > 5:
                    logging.info(f"Extracted UID via pattern {pattern}: {uid}")
                    return uid
    except Exception as e:
        logging.debug(f"Could not extract UID via page source patterns: {e}")

    logging.warning("Could not extract Facebook UID using any method.")
    return "" # Return empty string if not found

# --- NST Browser Integration ---

def get_remote_chrome_version(debugger_address):
    """Query the remote debugging endpoint to retrieve the actual Chrome version."""
    try:
        url = f"http://{debugger_address}/json/version"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        browser_info = data.get("Browser", "")
        import re
        version_match = re.search(r'Chrome/(\d+\.\d+\.\d+\.\d+)', browser_info)
        if version_match:
            version = version_match.group(1)
            logging.info(f"Detected remote Chrome version: {version}")
            return version
        else:
            logging.error("Could not extract Chrome version from remote debugger info.")
    except Exception as e:
        logging.error(f"Error retrieving remote Chrome version: {e}")
    return None

def get_debugger_port(url, max_retries=10, retry_interval=5):
    """Retrieve the debugger port by polling the specified URL."""
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()
            return data.get('data', {}).get('port')
        except requests.RequestException as e:
            logging.warning(f"Error retrieving debugger port: {e}. Retrying...")
            time.sleep(retry_interval)
    logging.critical("Failed to retrieve debugger port.")
    return None

def launch_and_connect_to_browser(profile_id, max_retries=10, retry_interval=5, headless=True):
    """Launch browser for the given profile and retrieve debugger address."""
    api_key = 'fc63ee6b-0785-4b2a-a179-d6ae22c88479'
    host = '127.0.0.1'
    config = {'headless': headless, 'autoClose': True}
    query = urlencode({'x-api-key': api_key, 'config': quote(json.dumps(config))})
    url = f'http://{host}:8848/devtool/launch/{profile_id}?{query}'

    port = get_debugger_port(url, max_retries, retry_interval)
    if port:
        return f"{host}:{port}"
    return None

def close_nst_browser_profile(profile_id):
    """Close NST Browser profile using API."""
    try:
        api_key = 'fc63ee6b-0785-4b2a-a179-d6ae22c88479'
        host = '127.0.0.1'
        url = f'http://{host}:8848/devtool/close/{profile_id}'

        headers = {'x-api-key': api_key}
        response = requests.post(url, headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                logging.info(f"Successfully closed NST Browser profile {profile_id}")
                return True

        logging.warning(f"Failed to close NST Browser profile {profile_id} via API")
        return False

    except Exception as e:
        logging.error(f"Error closing NST Browser profile {profile_id}: {e}")
        return False

def kill_nstchrome_processes():
    """Kill nstchrome.exe processes."""
    try:
        os.system("taskkill /F /IM nstchrome.exe > NUL 2>&1")
        logging.info("Killed nstchrome.exe processes")
    except Exception as e:
        logging.error(f"Error killing nstchrome processes: {e}")

def close_nst_browser_safely(profile_id):
    """Close NST Browser profile safely using API first, then kill processes if needed."""
    try:
        # First try to close via API
        if close_nst_browser_profile(profile_id):
            time.sleep(1)
            return True

        # If API close failed, kill processes
        logging.info(f"API close failed for profile {profile_id}, killing processes")
        kill_nstchrome_processes()
        time.sleep(1)
        return True

    except Exception as e:
        logging.error(f"Error closing NST Browser safely for profile {profile_id}: {e}")
        return False

def log_activity(action, details=None, post_id=None, account_id=None, status=None):
    """Log activity to the database for tracking and debugging."""
    try:
        with QMutexLocker(db_mutex):
            c.execute("""
                INSERT INTO activity_log (action, details, post_id, account_id, status)
                VALUES (?, ?, ?, ?, ?)
            """, (action, details, post_id, account_id, status))
            conn.commit()
    except Exception as e:
        logging.error(f"Error logging activity: {e}")

def load_app_settings():
    """Load application settings from file."""
    settings_file = "app_settings.json"
    default_settings = {
        'auto_publish': True,
        'check_interval': 60,
        'max_concurrent_posts': 1,
        'retry_failed_posts': True,
        'notification_enabled': True,
        'log_level': 'INFO'
    }

    try:
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                # Merge with defaults to ensure all keys exist
                for key, value in default_settings.items():
                    if key not in settings:
                        settings[key] = value
                return settings
    except Exception as e:
        logging.error(f"Error loading settings: {e}")

    return default_settings

def save_app_settings(settings):
    """Save application settings to file."""
    settings_file = "app_settings.json"
    try:
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=2, ensure_ascii=False)
        logging.info("Settings saved successfully")
    except Exception as e:
        logging.error(f"Error saving settings: {e}")

def get_nst_profiles_list():
    """Get list of available NST Browser profiles."""
    try:
        api_key = 'fc63ee6b-0785-4b2a-a179-d6ae22c88479'
        host = '127.0.0.1'
        url = f'http://{host}:8848/devtool/profiles'

        headers = {'x-api-key': api_key}
        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0 and 'data' in data:
                profiles = data['data']
                logging.info(f"Found {len(profiles)} NST Browser profiles")
                return profiles

        logging.warning("Failed to get NST profiles list")
        return []

    except Exception as e:
        logging.error(f"Error getting NST profiles list: {e}")
        return []

def check_nst_profile_status(profile_id):
    """Check NST Browser profile status and Facebook account status using Facebook Settings."""
    driver = None
    try:
        logging.info(f"Starting status check for profile {profile_id}")

        # Kill any existing processes first to ensure clean start
        kill_nstchrome_processes()
        time.sleep(2)

        # Try to launch and connect in headless mode
        debugger_address = launch_and_connect_to_browser(profile_id, headless=True)
        if not debugger_address:
            logging.warning(f"Failed to launch browser for profile {profile_id}")
            return {"status": "offline", "facebook_status": "unknown", "error": "Cannot launch browser"}

        # Connect with Selenium in headless mode for faster checking
        driver = exec_selenium_nst_headless(debugger_address)
        if not driver:
            logging.warning(f"Failed to connect Selenium for profile {profile_id}")
            return {"status": "online", "facebook_status": "unknown", "error": "Cannot connect Selenium"}

        try:
            # First, get Facebook ID from /me endpoint
            facebook_id = None
            try:
                logging.info(f"Getting Facebook ID for profile {profile_id}")
                driver.get("https://www.facebook.com/me")
                time.sleep(3)

                current_url = driver.current_url
                logging.info(f"Profile {profile_id} /me URL: {current_url}")

                # Extract Facebook ID from the redirected URL
                if "profile.php?id=" in current_url:
                    facebook_id = current_url.split("profile.php?id=")[1].split("&")[0]
                    logging.info(f"Extracted Facebook ID: {facebook_id}")
                elif "facebook.com/" in current_url and not "login" in current_url:
                    # For custom usernames, try to get numeric ID from page source
                    try:
                        page_source = driver.page_source
                        import re
                        match = re.search(r'"entity_id":"(\d+)"', page_source)
                        if match:
                            facebook_id = match.group(1)
                            logging.info(f"Extracted Facebook ID from entity_id: {facebook_id}")
                    except:
                        pass

            except Exception as e:
                logging.warning(f"Could not extract Facebook ID for profile {profile_id}: {e}")

            logging.info(f"Navigating to Facebook settings for profile {profile_id}")
            # Navigate directly to Facebook Settings page for accurate status check
            driver.get("https://web.facebook.com/settings")
            time.sleep(5)  # Wait for page to load

            current_url = driver.current_url.lower()
            logging.info(f"Profile {profile_id} current URL: {current_url}")

            # Simple check: if we can access settings page normally, account is live
            if "settings" in current_url and "facebook.com" in current_url:
                fb_status = "live"
            elif "login" in current_url:
                fb_status = "not_logged_in"
            elif "checkpoint" in current_url:
                fb_status = "checkpoint"
            else:
                # For any other case, check page content briefly
                page_source = driver.page_source.lower()
                if "disabled" in page_source or "suspended" in page_source:
                    fb_status = "banned"
                else:
                    fb_status = "unknown"

            logging.info(f"Profile {profile_id} status check complete: online - {fb_status}")
            result = {
                "status": "online",
                "facebook_status": fb_status,
                "url": driver.current_url,
                "title": driver.title,
                "checked_via": "settings_page"
            }

            # Add Facebook ID if extracted
            if facebook_id:
                result["facebook_id"] = facebook_id

            return result

        except Exception as e:
            logging.error(f"Error during status check for profile {profile_id}: {e}")
            return {
                "status": "online",
                "facebook_status": "error",
                "error": f"Check failed: {str(e)}"
            }

    except Exception as e:
        logging.error(f"Error checking NST profile {profile_id}: {e}")
        return {"status": "error", "facebook_status": "unknown", "error": str(e)}

    finally:
        # Ensure proper cleanup
        if driver:
            try:
                logging.info(f"Closing WebDriver for profile {profile_id}")
                driver.quit()
            except Exception as e:
                logging.warning(f"Error closing WebDriver for profile {profile_id}: {e}")

        # Close NST Browser safely to free up the profile for posting
        try:
            logging.info(f"Closing NST Browser safely for profile {profile_id}")
            close_nst_browser_safely(profile_id)
            time.sleep(2)  # Increased delay to ensure processes are closed
        except Exception as e:
            logging.warning(f"Error closing NST Browser safely for profile {profile_id}: {e}")

        logging.info(f"Status check cleanup completed for profile {profile_id}")

def extract_facebook_pages_and_groups(profile_id):
    """Extract Facebook pages and groups IDs that the account manages."""
    try:
        # Launch browser in headless mode for faster extraction
        debugger_address = launch_and_connect_to_browser(profile_id, headless=True)
        if not debugger_address:
            raise Exception("Cannot launch NST Browser")

        driver = exec_selenium_nst_headless(debugger_address)
        if not driver:
            raise Exception("Cannot connect Selenium")

        pages = []
        groups = []

        try:
            # Extract Pages IDs - Focus on managed pages only
            logging.info("Extracting managed Facebook Pages IDs...")

            # Navigate directly to pages manager - shows only pages you manage
            managed_pages_urls = [
                "https://www.facebook.com/pages/?category=your_pages",  # Your pages only
                "https://business.facebook.com/latest/home",  # Business manager
                "https://www.facebook.com/pages/",  # General pages manager
            ]

            for url in managed_pages_urls:
                try:
                    driver.get(url)
                    time.sleep(5)

                    # Use JavaScript to extract managed pages only
                    pages_data = driver.execute_script("""
                        var pagesList = [];

                        // Look for pages in the pages manager - focus on managed pages
                        var pageElements = document.querySelectorAll('a[href*="/pages/"], a[data-testid*="page"], div[role="button"] a[href*="facebook.com/"], a[href*="page_id="]');

                        pageElements.forEach(function(element) {
                            try {
                                var href = element.href || element.getAttribute('href');
                                var name = element.textContent || element.innerText || '';

                                // Get name from parent elements if not found
                                if (!name.trim() && element.parentElement) {
                                    var parentText = element.parentElement.textContent || '';
                                    if (parentText.trim()) {
                                        name = parentText.trim();
                                    }
                                }

                                if (href && href.includes('facebook.com') && name.trim()) {
                                    // Extract page ID from various URL patterns
                                    var pageId = null;

                                    // Pattern 1: /pages/page-name/page-id/
                                    var pagesMatch = href.match(/\\/pages\\/[^\\/]+\\/(\\d+)/);
                                    if (pagesMatch) {
                                        pageId = pagesMatch[1];
                                    }

                                    // Pattern 2: page_id parameter
                                    else if (href.includes('page_id=')) {
                                        var pageIdMatch = href.match(/page_id=(\\d+)/);
                                        if (pageIdMatch) {
                                            pageId = pageIdMatch[1];
                                        }
                                    }

                                    // Pattern 3: facebook.com/page-name (for custom URLs)
                                    else if (href.match(/facebook\\.com\\/[^?\\/]+$/)) {
                                        var pageName = href.split('facebook.com/')[1];
                                        if (pageName && !pageName.includes('/') && pageName !== 'pages' && pageName !== 'groups' && pageName !== 'home') {
                                            pageId = pageName;
                                        }
                                    }

                                    // Only add if we found a valid page ID and it's not a duplicate
                                    if (pageId && name.trim().length > 1 && !pagesList.some(p => p.page_id === pageId)) {
                                        // Filter out navigation links
                                        if (!href.includes('/feed/') && !href.includes('/discover/') && !href.includes('/settings/')) {
                                            pagesList.push({
                                                page_id: pageId,
                                                name: name.trim(),
                                                url: href,
                                                type: 'page'
                                            });
                                        }
                                    }
                                }
                            } catch (e) {
                                // Skip invalid elements
                            }
                        });

                        return pagesList;
                    """)

                    if pages_data:
                        # Add new pages (avoid duplicates)
                        for page_data in pages_data:
                            if not any(p['page_id'] == page_data['page_id'] for p in pages):
                                pages.append(page_data)

                        logging.info(f"Found {len(pages_data)} managed pages from {url}")

                except Exception as e:
                    logging.warning(f"Could not extract pages from {url}: {e}")
                    continue
            if not pages:
                logging.warning("No managed pages found")

            # Extract Groups IDs - Focus on managed groups only
            logging.info("Extracting managed Facebook Groups IDs...")

            # Navigate to groups where user is admin/moderator
            managed_group_urls = [
                "https://www.facebook.com/groups/?category=admin",  # Groups where user is admin
                "https://www.facebook.com/groups/?category=moderator",  # Groups where user is moderator
                "https://www.facebook.com/groups/",  # General groups page
                "https://www.facebook.com/groups/discover/",  # Alternative discovery page
            ]

            for url in managed_group_urls:
                try:
                    driver.get(url)
                    time.sleep(5)

                    # Use JavaScript to extract managed groups only
                    groups_data = driver.execute_script("""
                        var groupsList = [];

                        // Look for group elements - focus on managed groups
                        var groupElements = document.querySelectorAll('a[href*="/groups/"], div[data-testid*="group"] a, div[role="article"] a[href*="/groups/"], div[role="button"] a[href*="/groups/"]');

                        groupElements.forEach(function(element) {
                            try {
                                var href = element.href || element.getAttribute('href');
                                var name = element.textContent || element.innerText || '';

                                // Get group name from nearby elements if not in the link itself
                                if (!name.trim() && element.parentElement) {
                                    var parentText = element.parentElement.textContent || '';
                                    if (parentText.trim()) {
                                        name = parentText.trim();
                                    }
                                }

                                // Also check for admin/moderator indicators
                                var isManaged = false;
                                var parentContainer = element.closest('div[role="article"], div[data-testid*="group"]');
                                if (parentContainer) {
                                    var containerText = parentContainer.textContent || '';
                                    // Look for admin/moderator indicators
                                    if (containerText.includes('Admin') || containerText.includes('Moderator') ||
                                        containerText.includes('إدارة') || containerText.includes('مشرف')) {
                                        isManaged = true;
                                    }
                                }

                                if (href && href.includes('/groups/') && name.trim()) {
                                    // Extract group ID from URL
                                    var groupId = null;

                                    // Pattern 1: /groups/numeric-id/
                                    var numericMatch = href.match(/\\/groups\\/(\\d+)/);
                                    if (numericMatch) {
                                        groupId = numericMatch[1];
                                    }

                                    // Pattern 2: /groups/group-name/ (custom URL)
                                    else {
                                        var groupMatch = href.match(/\\/groups\\/([^\\/\\?]+)/);
                                        if (groupMatch && groupMatch[1] !== 'feed' && groupMatch[1] !== 'discover' &&
                                            groupMatch[1] !== 'create' && groupMatch[1] !== 'browse') {
                                            groupId = groupMatch[1];
                                        }
                                    }

                                    // Only add if we found a valid group ID and it's not a duplicate
                                    if (groupId && name.trim().length > 1 && !groupsList.some(g => g.group_id === groupId)) {
                                        // Filter out navigation links and non-group content
                                        if (!href.includes('/feed/') && !href.includes('/discover/') &&
                                            !href.includes('/membership/') && !href.includes('/join/') &&
                                            !href.includes('/create/') && !href.includes('/browse/')) {
                                            groupsList.push({
                                                group_id: groupId,
                                                name: name.trim(),
                                                url: href,
                                                type: 'group',
                                                is_managed: isManaged
                                            });
                                        }
                                    }
                                }
                            } catch (e) {
                                // Skip invalid elements
                            }
                        });

                        return groupsList;
                    """)

                    if groups_data:
                        # Add new groups (avoid duplicates)
                        for group_data in groups_data:
                            if not any(g['group_id'] == group_data['group_id'] for g in groups):
                                groups.append(group_data)

                        logging.info(f"Found {len(groups_data)} managed groups from {url}")

                except Exception as e:
                    logging.warning(f"Could not extract groups from {url}: {e}")
                    continue

            return {
                'success': True,
                'pages': pages,
                'groups': groups,
                'profile_id': profile_id
            }

        finally:
            try:
                driver.quit()
            except:
                pass

            # Close NST Browser profile properly using API
            try:
                close_nst_browser_profile(profile_id)
                time.sleep(1)
            except:
                pass

            # Also kill local processes as backup
            try:
                kill_nstchrome_processes()
                time.sleep(1)
            except:
                pass

    except Exception as e:
        logging.error(f"Error extracting pages/groups for profile {profile_id}: {e}")

        # Close profile and kill processes even if there was an error
        try:
            close_nst_browser_profile(profile_id)
            time.sleep(1)
        except:
            pass

        try:
            kill_nstchrome_processes()
            time.sleep(1)
        except:
            pass

        return {
            'success': False,
            'error': str(e),
            'pages': [],
            'groups': [],
            'profile_id': profile_id
        }

def exec_selenium_nst(debugger_address):
    """Start Selenium WebDriver using the debugger address."""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager

        options = Options()
        options.debugger_address = debugger_address

        # Try to get the remote Chrome version for better compatibility
        remote_version = get_remote_chrome_version(debugger_address)
        if remote_version:
            service = Service(ChromeDriverManager(driver_version=remote_version).install())
        else:
            service = Service(ChromeDriverManager().install())

        driver = webdriver.Chrome(service=service, options=options)
        logging.info(f"Connected to NST Browser at {debugger_address}")
        return driver
    except Exception as e:
        logging.error(f"Failed to initialize WebDriver: {e}")
        return None

def exec_selenium_nst_headless(debugger_address):
    """Start Selenium WebDriver using the debugger address in headless mode."""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager

        options = Options()
        options.debugger_address = debugger_address

        # Add minimal options for headless operation
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-images')  # Don't load images for faster processing

        # Try to get the remote Chrome version for better compatibility
        remote_version = get_remote_chrome_version(debugger_address)
        if remote_version:
            service = Service(ChromeDriverManager(driver_version=remote_version).install())
        else:
            service = Service(ChromeDriverManager().install())

        driver = webdriver.Chrome(service=service, options=options)
        logging.info(f"Connected to NST Browser at {debugger_address} (headless mode)")
        return driver
    except Exception as e:
        logging.error(f"Failed to initialize headless WebDriver: {e}")
        return None

def create_driver_for_account(account: AccountData):
    """Create NST Browser driver for account."""
    if not account.nst_profile_id:
        raise Exception(f"Account {account.full_name} does not have NST Profile ID configured")

    # Use NST Browser only
    kill_nstchrome_processes()
    time.sleep(2)

    debugger_address = launch_and_connect_to_browser(account.nst_profile_id)
    if not debugger_address:
        raise Exception(f"Failed to launch NST Browser for profile {account.nst_profile_id}")

    driver = exec_selenium_nst(debugger_address)
    if not driver:
        raise Exception(f"Failed to connect to NST Browser for profile {account.nst_profile_id}")

    logging.info(f"Created NST Browser driver for account {account.full_name}")
    return driver

def _load_cookies_and_navigate(driver, cookie_str, url):
    """Loads cookies into the driver and navigates to a URL."""
    driver.get("https://www.facebook.com") # Go to domain first to set cookies
    time.sleep(1) # Small delay
    # Improved cookie loading
    cookies_loaded_count = 0
    try:
        # Try parsing as JSON first (more robust if saved correctly)
        try:
            cookies_list = json.loads(cookie_str)
            for cookie in cookies_list:
                # Ensure essential keys are present
                if 'name' in cookie and 'value' in cookie:
                    # Selenium expects domain, path etc. Add defaults if missing.
                    cookie.setdefault('domain', '.facebook.com')
                    cookie.setdefault('path', '/')
                    # Remove problematic keys if they exist
                    cookie.pop('expiry', None)
                    cookie.pop('httpOnly', None) # Sometimes causes issues
                    cookie.pop('sameSite', None) # Can be problematic

                    try:
                        driver.add_cookie(cookie)
                        cookies_loaded_count += 1
                    except Exception as e:
                        logging.warning(f"Failed to add cookie (JSON): {cookie.get('name', 'N/A')} - {e}")
        except json.JSONDecodeError:
            logging.debug("Cookie string is not JSON, trying key:value format.")
            # Fallback to key:value parsing
            for line in cookie_str.splitlines():
                if ":" in line:
                    name, value = line.strip().split(":", 1)
                    name = name.strip()
                    value = value.strip()
                    if name and value:
                        try:
                            driver.add_cookie({"name": name, "value": value, "domain": ".facebook.com", "path": "/"})
                            cookies_loaded_count += 1
                        except Exception as e:
                            logging.warning(f"Failed to add cookie (key:value): {name} - {e}")

        logging.debug(f"Loaded {cookies_loaded_count} cookies.")
        if cookies_loaded_count == 0:
             raise ValueError("No valid cookies could be loaded.")

        driver.get(url)
        time.sleep(3) # Allow page to load after cookie injection + navigation
        return True
    except Exception as e:
        logging.error(f"Failed to load cookies or navigate: {e}", exc_info=True)
        return False

def _fetch_pages_selenium(driver, cookie_str):
    """Fetches pages managed by the account."""
    if not _load_cookies_and_navigate(driver, cookie_str, "https://www.facebook.com/pages/?category=your_pages"):
        return [], ValueError("Failed to load cookies or navigate to pages.")
    
    pages = []
    try:
        # Scroll down to load all pages (might need adjustments)
        last_height = driver.execute_script("return document.body.scrollHeight")
        for _ in range(3): # Scroll a few times
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height

        # Use more specific selectors if possible (inspect FB page structure)
        # This is a generic example, likely needs updating based on current FB structure
        page_elements = driver.find_elements(By.XPATH, "//a[contains(@href, '/pages/')]") # Very generic

        # Refined script to extract data (adjust selectors based on FB structure)
        pages = driver.execute_script("""
            var pagesList = [];
            // Example: Find elements containing page links and names. Adjust selector!
            var elems = document.querySelectorAll('div[role="presentation"] a[href*="/pages/"]');
            elems.forEach(el => {
                var nameElement = el.querySelector('span'); // Adjust if name is elsewhere
                var name = nameElement ? nameElement.innerText.trim() : el.innerText.trim(); // Get name
                var href = el.href;
                if (name && href) {
                    var idMatch = href.match(/pages\\/(\\d+)/) || href.match(/\\/(\\d+)(\\/?$|\\/\\?)/); // Try different ID patterns
                    var id = idMatch ? idMatch[1] : "";
                    // Basic filtering to avoid irrelevant links
                    if (id && name.length > 1 && name !== 'See More') {
                        pagesList.push({name: name, id: id, link: href});
                    }
                }
            });
            // Remove duplicates based on ID
            const uniquePages = Array.from(new Map(pagesList.map(p => [p.id, p])).values());
            return uniquePages;
        """)
        logging.info(f"Fetched {len(pages)} unique pages.")
    except Exception as e:
        logging.error(f"Error fetching pages with Selenium: {e}", exc_info=True)
        return [], e # Return error object
        
    return pages, None # Return pages and no error

def _fetch_groups_selenium(driver, cookie_str):
    """Fetches groups the account is in."""
    if not _load_cookies_and_navigate(driver, cookie_str, "https://www.facebook.com/groups/feed/"): # Go to groups feed
        return [], ValueError("Failed to load cookies or navigate to groups.")

    groups = []
    try:
         # Scroll down to load groups (might need adjustments)
        last_height = driver.execute_script("return document.body.scrollHeight")
        for _ in range(5): # Scroll more for groups potentially
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2.5)
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height

        # Script to extract group data (adjust selectors based on FB structure)
        groups = driver.execute_script("""
            var groupsList = [];
            // Example: Look for links within a specific group list container. Adjust selector!
            var elems = document.querySelectorAll('a[href*="/groups/"]');
            elems.forEach(el => {
                var nameElement = el.querySelector('span'); // Adjust selector
                var name = nameElement ? nameElement.innerText.trim() : el.innerText.trim();
                var href = el.href;
                if (name && href && href.includes('/groups/')) {
                    // Extract ID: digits between /groups/ and next / or end of string
                    var idMatch = href.match(/\\/groups\\/(\\d+)/);
                    var id = idMatch ? idMatch[1] : "";
                    // Filter out common irrelevant links and ensure ID looks valid
                    if (id && name.length > 1 && !href.includes('/feed/') && !href.includes('/membership/') && !href.includes('/join/')) {
                        groupsList.push({name: name, id: id, link: href});
                    }
                }
            });
            // Remove duplicates based on ID
            const uniqueGroups = Array.from(new Map(groupsList.map(g => [g.id, g])).values());
            return uniqueGroups;
        """)
        logging.info(f"Fetched {len(groups)} unique groups.")
    except Exception as e:
        logging.error(f"Error fetching groups with Selenium: {e}", exc_info=True)
        return [], e

    return groups, None

# --- Worker Threads ---

class BaseWorker(QThread):
    """Base worker thread for handling signals."""
    finished = pyqtSignal(object, object) # result, error
    progress = pyqtSignal(str) # status message

    def __init__(self, target_func, *args, **kwargs):
        super().__init__()
        self._target_func = target_func
        self._args = args
        self._kwargs = kwargs

    def run(self):
        result = None
        error = None
        try:
            self.progress.emit(f"Starting: {self._target_func.__name__}...")
            # For Selenium tasks, use the runner
            if "selenium" in self._target_func.__name__:
                 result, error = selenium_task_runner(self._target_func, *self._args, **self._kwargs)
            else:
                 # For non-Selenium tasks (e.g., database checks)
                 result = self._target_func(*self._args, **self._kwargs)
            if error:
                 self.progress.emit(f"Error in {self._target_func.__name__}: {error}")
            else:
                 self.progress.emit(f"Finished: {self._target_func.__name__}.")
        except Exception as e:
            error = e
            logging.error(f"Unexpected error in worker {self._target_func.__name__}: {e}", exc_info=True)
            self.progress.emit(f"Critical error in worker: {e}")
        finally:
            self.finished.emit(result, error)


class CheckPostsWorker(QThread):
    """Background worker to check for scheduled posts and publish them automatically."""
    postsStatusChanged = pyqtSignal(list) # Emit list of updated post IDs
    errorOccurred = pyqtSignal(str)
    postPublished = pyqtSignal(int, str) # Emit (post_id, status) when post is published
    postingProgress = pyqtSignal(str) # Emit progress messages

    def __init__(self, parent=None):
        super().__init__(parent)
        self._running = True
        # Each worker thread needs its own DB connection for safety with check_same_thread=False
        self.local_conn = None
        self.local_cursor = None

    def run(self):
        try:
            self.local_conn = sqlite3.connect(DB_FILE, check_same_thread=False)
            self.local_conn.row_factory = sqlite3.Row
            self.local_cursor = self.local_conn.cursor()
            logging.info("CheckPostsWorker started with new DB connection.")
        except sqlite3.Error as e:
            logging.critical(f"CheckPostsWorker failed to connect to DB: {e}")
            self.errorOccurred.emit(f"Database connection error: {e}")
            self._running = False # Stop the thread if DB fails

        while self._running:
            updated_post_ids = []
            try:
                # Use CURRENT_TIMESTAMP directly in SQL for better accuracy
                # Find posts scheduled for the past, status 'pending'
                # Use strftime for comparison as DATETIME might be stored as text
                # Corrected query: Compare scheduled_time (assumed stored as TEXT ISO format or UNIX timestamp)
                # If stored as ISO string:
                # query = "SELECT id FROM posts WHERE status='pending' AND scheduled_time <= strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime')"
                # If stored as UNIX timestamp (more reliable):
                now_ts = time.time()
                query = "SELECT id FROM posts WHERE status='pending' AND scheduled_time <= ?"

                pending_posts = self.local_cursor.execute(query, (now_ts,)).fetchall()

                if pending_posts:
                    logging.info(f"🚀 Found {len(pending_posts)} pending posts ready for publishing.")
                    self.postingProgress.emit(f"🚀 وجدت {len(pending_posts)} منشورات جاهزة للنشر...")
                    for row in pending_posts:
                        post_id = row['id']
                        try:
                            # Get full post details for publishing
                            post_details = self.local_cursor.execute("""
                                SELECT p.*, a.nst_profile_id, a.full_name as account_name, a.fb_id, a.browser_type,
                                       pg.page_id, pg.page_name, gr.group_id, gr.group_name
                                FROM posts p
                                LEFT JOIN accounts a ON p.account_id = a.id
                                LEFT JOIN pages pg ON p.target_page_id = pg.id
                                LEFT JOIN groups gr ON p.target_group_id = gr.id
                                WHERE p.id = ?
                            """, (post_id,)).fetchone()

                            if not post_details:
                                logging.warning(f"Post {post_id} not found, skipping.")
                                continue

                            # Send progress signal before publishing
                            target_name = post_details.get('target_info', {}).get('name', 'Timeline')
                            self.postingProgress.emit(f"🚀 جاري نشر المنشور {post_id} على {target_name}...")

                            # Attempt to publish the post
                            success = self.publish_post(post_details)

                            if success:
                                # Update status to 'posted'
                                self.local_cursor.execute("UPDATE posts SET status='posted' WHERE id=?", (post_id,))
                                updated_post_ids.append(post_id)
                                logging.info(f"Successfully published post ID {post_id}")
                                self.postPublished.emit(post_id, "posted")
                                self.postingProgress.emit(f"✅ تم نشر المنشور {post_id} بنجاح!")
                                # Log successful publication
                                log_activity("post_published", f"Post published successfully", post_id, post_details.get('account_id'), "success")
                            else:
                                # Update status to 'failed'
                                self.local_cursor.execute("UPDATE posts SET status='failed' WHERE id=?", (post_id,))
                                self.postingProgress.emit(f"❌ فشل نشر المنشور {post_id}")
                                updated_post_ids.append(post_id)
                                logging.error(f"Failed to publish post ID {post_id}")
                                self.postPublished.emit(post_id, "failed")
                                # Log failed publication
                                log_activity("post_failed", f"Post publication failed", post_id, post_details.get('account_id'), "failed")

                        except Exception as publish_err:
                            logging.exception(f"Error publishing post ID {post_id}: {publish_err}")
                            # Update to failed status
                            try:
                                self.local_cursor.execute("UPDATE posts SET status='failed' WHERE id=?", (post_id,))
                                updated_post_ids.append(post_id)
                                self.postPublished.emit(post_id, "failed")
                            except:
                                pass

                    if updated_post_ids:
                         self.local_conn.commit() # Commit after processing batch
                         self.postsStatusChanged.emit(updated_post_ids) # Emit IDs that changed

            except sqlite3.Error as e:
                logging.error(f"CheckPostsWorker database error: {e}")
                self.errorOccurred.emit(f"Database check error: {e}")
                # Avoid busy-waiting on critical DB error
                self.msleep(15000) # Wait longer before retrying
            except Exception as e:
                logging.error(f"CheckPostsWorker unexpected error: {e}", exc_info=True)
                self.errorOccurred.emit(f"Unexpected worker error: {e}")
                self.msleep(15000)

            # Sleep for a while before checking again
            # Check every 30 seconds, check running flag frequently within sleep
            for _ in range(300): # 300 * 100ms = 30 seconds
                if not self._running:
                    break
                self.msleep(100)

        # Cleanup connection when thread stops
        if self.local_conn:
            try:
                self.local_conn.close()
                logging.info("CheckPostsWorker database connection closed.")
            except sqlite3.Error as e:
                logging.error(f"Error closing CheckPostsWorker DB connection: {e}")

    def publish_post(self, post_details):
        """Publish a single post using NST Browser."""
        try:
            # Check if account has NST profile configured
            if not post_details['nst_profile_id']:
                logging.warning(f"Post {post_details['id']} has no NST profile configured, skipping.")
                return False

            # Create account data object
            account_data = AccountData(
                id=post_details.get('account_id'),
                fb_id="",  # Not needed for NST Browser posting
                full_name=post_details.get('account_name', 'Unknown'),
                cookie="",  # Not needed for NST Browser posting
                status="active",
                nst_profile_id=post_details['nst_profile_id'],
                browser_type="nst_browser"
            )

            # Prepare post content
            content = post_details['content'] or ""
            if post_details['recipe_text']:
                content += f"\n\n{post_details['recipe_text']}"
            if post_details['website_link']:
                content += f"\n\n{post_details['website_link']}"

            # Prepare images
            images = []
            if post_details['image_path']:
                image_paths = post_details['image_path'].split(',')
                for img_path in image_paths:
                    img_path = img_path.strip()
                    if img_path and os.path.exists(img_path):
                        images.append(img_path)

            # Determine target (page or group) from target_info
            target = {
                'target_type': post_details.get('target_type', 'profile'),
                'target_info': post_details.get('target_info', {})
            }

            target_type = target['target_type']
            target_info = target['target_info']

            if target_type == 'page' and target_info:
                page_id = target_info.get('page_id') or target_info.get('id')
                page_name = target_info.get('name', 'Unknown Page')
                if page_id:
                    logging.info(f"📄 Targeting page: {page_name} ({page_id})")
                else:
                    logging.warning("Page target specified but no page ID found")
            elif target_type == 'group' and target_info:
                group_id = target_info.get('group_id') or target_info.get('id')
                group_name = target_info.get('name', 'Unknown Group')
                if group_id:
                    logging.info(f"👥 Targeting group: {group_name} ({group_id})")
                else:
                    logging.warning("Group target specified but no group ID found")
            else:
                logging.info("📝 Posting to timeline (no specific target)")

            # Attempt to post
            logging.info(f"🚀 Publishing post {post_details['id']} using NST profile {post_details['nst_profile_id']}")
            success = self.post_to_facebook_nst(account_data, content, images, target)

            return success

        except Exception as e:
            logging.error(f"Error in publish_post for post {post_details['id']}: {e}")
            return False

    def post_to_facebook_nst(self, account: AccountData, content: str, images: list = None, target: dict = None):
        """Post to Facebook using NST Browser - enhanced version with target selection."""
        driver = None
        try:
            # Kill any existing processes
            kill_nstchrome_processes()
            time.sleep(2)

            # Launch NST Browser (NOT headless for better interaction)
            debugger_address = launch_and_connect_to_browser(account.nst_profile_id, headless=False)
            if not debugger_address:
                logging.error(f"Failed to launch NST Browser for profile {account.nst_profile_id}")
                return False

            # Connect Selenium
            driver = exec_selenium_nst(debugger_address)  # Use non-headless version
            if not driver:
                logging.error(f"Failed to connect Selenium to NST Browser")
                return False

            # Navigate to target location based on type
            if target and isinstance(target, dict):
                target_type = target.get('target_type', 'profile')
                target_info = target.get('target_info', {})

                if target_type == 'group' and target_info.get('id'):
                    # Navigate to specific group
                    group_id = target_info['id']
                    group_url = f"https://www.facebook.com/groups/{group_id}"
                    logging.info(f"Navigating to group: {group_url}")
                    driver.get(group_url)
                    time.sleep(5)
                elif target_type == 'page' and target_info.get('id'):
                    # Navigate to specific page
                    page_id = target_info['id']
                    page_url = f"https://www.facebook.com/{page_id}"
                    logging.info(f"Navigating to page: {page_url}")
                    driver.get(page_url)
                    time.sleep(5)
                else:
                    # Default to main Facebook timeline
                    driver.get("https://www.facebook.com")
                    time.sleep(5)
            elif target and isinstance(target, str):
                # Legacy string target support
                driver.get(target)
                time.sleep(5)
                logging.info(f"Successfully navigated to target: {target}")
            else:
                # Default to main Facebook timeline
                driver.get("https://www.facebook.com")
                time.sleep(5)

            # Check if logged in
            if "login" in driver.current_url.lower():
                logging.error(f"NST profile {account.nst_profile_id} is not logged in to Facebook")
                return False

            # Find post creation area
            try:
                # Step 1: Find and click the "Write something..." area to open post dialog
                logging.info("Looking for 'Write something...' area...")
                write_something_selectors = [
                    # Group post creation selectors
                    "div[role='button'][aria-label*='Write something']",
                    "div[data-testid='react-composer-root']",
                    "div[aria-label*='Write something']",
                    "div[placeholder*='Write something']",
                    # Alternative selectors for groups
                    "div[role='textbox'][placeholder*='Write something']",
                    "textarea[placeholder*='Write something']",
                    # Fallback selectors
                    "div[role='button'][data-testid='status-attachment-mentions-input']"
                ]

                write_area = None
                for selector in write_something_selectors:
                    try:
                        write_area = WebDriverWait(driver, 8).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                        logging.info(f"Found 'Write something' area with selector: {selector}")
                        break
                    except:
                        continue

                if not write_area:
                    logging.error("Could not find 'Write something' area")
                    # Take screenshot for debugging
                    try:
                        driver.save_screenshot("write_something_error.png")
                        logging.info("Screenshot saved as write_something_error.png")
                    except:
                        pass
                    return False

                # Click to open the post creation dialog
                logging.info("Clicking 'Write something' to open post dialog...")
                write_area.click()
                time.sleep(4)  # Wait for dialog to open

                # Step 2: Find the actual text input in the opened dialog
                logging.info("Looking for text input in post dialog...")
                dialog_text_selectors = [
                    # Post dialog text area selectors
                    "div[aria-label*='Create a public post']",
                    "div[data-testid='react-composer-root'] div[role='textbox']",
                    "div[contenteditable='true'][role='textbox']",
                    "div[aria-label*='What'][role='textbox']",
                    "textarea[placeholder*='What']",
                    # Fallback selectors
                    "div[role='textbox']"
                ]

                text_area = None
                for selector in dialog_text_selectors:
                    try:
                        text_area = WebDriverWait(driver, 8).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                        logging.info(f"Found text input in dialog with selector: {selector}")
                        break
                    except:
                        continue

                if not text_area:
                    logging.error("Could not find text input in post dialog")
                    try:
                        driver.save_screenshot("dialog_text_error.png")
                        logging.info("Screenshot saved as dialog_text_error.png")
                    except:
                        pass
                    return False

                # Step 3: Enter content in the dialog
                logging.info("Clicking on text area in dialog...")
                text_area.click()
                time.sleep(2)

                # Clear any existing content
                try:
                    text_area.clear()
                except:
                    # If clear() doesn't work, try selecting all and deleting
                    text_area.send_keys(Keys.CONTROL + "a")
                    text_area.send_keys(Keys.DELETE)
                time.sleep(1)

                # Enter content
                logging.info(f"Entering content: {content[:50]}...")
                text_area.send_keys(content)
                time.sleep(3)

                # Handle images if provided
                if images and len(images) > 0:
                    logging.info(f"Attempting to upload {len(images)} images...")
                    try:
                        # Enhanced photo/video button selectors for post dialog
                        photo_selectors = [
                            # Photo/video button in post creation dialog
                            "div[aria-label='Photo/video'][role='button']",
                            "div[data-testid='photo-video-button']",
                            "div[aria-label*='Photo'][role='button']",
                            "div[role='button'][aria-label*='Photo/video']",
                            # Alternative selectors for post dialog
                            "div[data-testid='react-composer-root'] div[aria-label*='Photo']",
                            "svg[aria-label*='Photo'] + ..",  # Parent of photo icon
                            # Direct file input (fallback)
                            "input[type='file'][accept*='image']"
                        ]

                        image_uploaded = False
                        for selector in photo_selectors:
                            try:
                                if selector.startswith("input"):
                                    # Direct file input
                                    file_input = WebDriverWait(driver, 5).until(
                                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                                    )
                                else:
                                    # Click photo button first
                                    photo_btn = WebDriverWait(driver, 5).until(
                                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                                    )
                                    logging.info(f"Clicking photo button with selector: {selector}")
                                    photo_btn.click()
                                    time.sleep(3)

                                    # Find file input after clicking
                                    file_input = WebDriverWait(driver, 10).until(
                                        EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
                                    )

                                # Upload all images
                                valid_images = [img for img in images if os.path.exists(img)]
                                if valid_images:
                                    # Upload multiple images at once
                                    all_paths = '\n'.join(valid_images)
                                    file_input.send_keys(all_paths)
                                    logging.info(f"Uploaded {len(valid_images)} images successfully")
                                    time.sleep(5)  # Wait for upload to complete
                                    image_uploaded = True
                                break
                            except Exception as selector_error:
                                logging.debug(f"Selector {selector} failed: {selector_error}")
                                continue

                        if not image_uploaded:
                            logging.warning("Could not find photo upload button")

                    except Exception as img_error:
                        logging.warning(f"Could not upload images: {img_error}")

                # Step 5: Find and click post button in the dialog
                logging.info("Looking for Post button in dialog...")
                post_selectors = [
                    # Post button in post creation dialog
                    "div[role='button'][aria-label='Post']",
                    "button[aria-label='Post']",
                    "div[data-testid='react-composer-post-button']",
                    "button[data-testid='react-composer-post-button']",
                    # Alternative selectors for post dialog
                    "div[aria-label='Post'][role='button']",
                    "div[data-testid='react-composer-root'] div[aria-label='Post']",
                    # Fallback selectors
                    "button[type='submit']",
                    "div[role='button']:contains('Post')"
                ]

                post_clicked = False
                for selector in post_selectors:
                    try:
                        post_btn = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                        logging.info(f"Clicking post button with selector: {selector}")
                        post_btn.click()
                        post_clicked = True
                        break
                    except Exception as btn_error:
                        logging.debug(f"Post button selector {selector} failed: {btn_error}")
                        continue

                if not post_clicked:
                    logging.error("Could not find or click post button")
                    return False

                # Step 6: Wait and verify post was published
                logging.info("Waiting for post to be published...")
                time.sleep(8)

                # Verify post was published by checking for success indicators
                success_indicators = [
                    # Success notifications
                    "div[role='alert']",
                    "div[data-testid='toast']",
                    "div[aria-label*='posted']",
                    "div[aria-label*='Your post is now published']",
                    # Check if dialog closed (indicates success)
                    "div[aria-label*='Create post']"  # If this is gone, post was likely successful
                ]

                post_confirmed = False

                # First check for success notifications
                for indicator in success_indicators[:-1]:  # Exclude the dialog check
                    try:
                        success_element = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, indicator))
                        )
                        if success_element:
                            logging.info(f"Post publication confirmed by success indicator: {indicator}")
                            post_confirmed = True
                            break
                    except:
                        continue

                # If no success notification found, check if dialog closed
                if not post_confirmed:
                    try:
                        # If we can't find the create post dialog anymore, it likely closed (success)
                        WebDriverWait(driver, 3).until_not(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "div[aria-label*='Create post']"))
                        )
                        logging.info("Post dialog closed - assuming post was successful")
                        post_confirmed = True
                    except:
                        # Dialog still there or other error
                        pass

                # Additional verification: check if composer is closed/reset
                if not post_confirmed:
                    try:
                        # If composer is reset/empty, it usually means post was successful
                        composer_check = driver.find_elements(By.CSS_SELECTOR, "div[role='textbox']")
                        if composer_check and not composer_check[0].text.strip():
                            post_confirmed = True
                            logging.info("Post confirmed by composer reset")
                    except:
                        pass

                if post_confirmed:
                    logging.info(f"✅ Successfully posted to Facebook using NST profile {account.nst_profile_id}")
                    logging.info(f"📝 Content: {content[:100]}{'...' if len(content) > 100 else ''}")
                    if images:
                        logging.info(f"🖼️ Images uploaded: {len(images)} files")
                    return True
                else:
                    logging.warning(f"⚠️ Post may have been published but confirmation unclear for NST profile {account.nst_profile_id}")
                    # Take a screenshot for manual verification
                    try:
                        driver.save_screenshot(f"unclear_post_status_{account.nst_profile_id}.png")
                        logging.info(f"Screenshot saved for manual verification: unclear_post_status_{account.nst_profile_id}.png")
                    except:
                        pass
                    return True  # Assume success if no clear failure

            except Exception as post_error:
                logging.error(f"❌ Error during posting process for NST profile {account.nst_profile_id}: {post_error}")
                # Take error screenshot for debugging
                try:
                    if driver:
                        driver.save_screenshot(f"posting_error_{account.nst_profile_id}.png")
                        logging.info(f"Error screenshot saved: posting_error_{account.nst_profile_id}.png")
                except:
                    pass
                return False

        except Exception as e:
            logging.error(f"❌ Critical error in post_to_facebook_nst for profile {account.nst_profile_id}: {e}")
            logging.error(f"📝 Failed content: {content[:100]}{'...' if len(content) > 100 else ''}")
            return False

        finally:
            # Enhanced cleanup with browser monitoring
            if driver:
                try:
                    # Keep browser open for a bit longer to monitor post status
                    logging.info(f"🔍 Keeping browser open for 10 seconds to monitor post status for profile {account.nst_profile_id}...")
                    time.sleep(10)

                    # Take a final screenshot for verification
                    try:
                        screenshot_path = f"post_verification_{account.nst_profile_id}_{int(time.time())}.png"
                        driver.save_screenshot(screenshot_path)
                        logging.info(f"📸 Verification screenshot saved: {screenshot_path}")
                    except Exception as screenshot_error:
                        logging.warning(f"Could not save verification screenshot: {screenshot_error}")

                    # Close browser
                    logging.info(f"🔒 Closing browser for NST profile {account.nst_profile_id}")
                    driver.quit()
                except Exception as cleanup_error:
                    logging.error(f"Error during browser cleanup: {cleanup_error}")
                    try:
                        driver.quit()
                    except:
                        pass
                    logging.info("Browser closed successfully")
                except Exception as cleanup_error:
                    logging.warning(f"Error during browser cleanup: {cleanup_error}")

            # Delayed NST Browser process cleanup
            try:
                # Wait a bit before killing processes to ensure post completion
                logging.info(f"⏳ Waiting 5 seconds before cleaning up NST Browser processes for profile {account.nst_profile_id}...")
                time.sleep(5)
                close_nst_browser_safely(account.nst_profile_id)
                logging.info(f"🧹 NST Browser processes cleaned up successfully for profile {account.nst_profile_id}")
            except Exception as process_error:
                logging.warning(f"⚠️ Error cleaning up NST Browser processes for profile {account.nst_profile_id}: {process_error}")

    def stop(self):
        """Signals the worker thread to stop."""
        logging.info("Stopping CheckPostsWorker...")
        self._running = False
        # No need to call quit() or wait() here if the loop checks _running frequently


# --- Custom Widgets ---

# HoursCalendarWidget: Seems reasonable, keep as is or slightly restyle.
class HoursCalendarWidget(QWidget):
    # (Keep existing implementation - maybe add minor styling)
    def __init__(self, parent=None, initial_selected_hours=None):
        super().__init__(parent)
        self.selected_hours = set(initial_selected_hours) if initial_selected_hours else set()
        # Generate hours dynamically
        self.hours = [datetime.time(h).strftime("%I:%M %p") for h in range(24)] # Simpler generation

        self.layout = QGridLayout(self)
        self.layout.setSpacing(4) # Tighter spacing
        self.layout.setContentsMargins(0, 0, 0, 0)
        col_count = 4

        for index, hour in enumerate(self.hours):
            btn = QPushButton(hour)
            btn.setCheckable(True)
            btn.setMinimumWidth(60) # Ensure buttons are wide enough
            # Use stylesheet from central theme later, but basic style here
            btn.setStyleSheet("""
                QPushButton {
                    border: 1px solid #CCCCCC;
                    border-radius: 3px;
                    padding: 5px;
                    background-color: #FFFFFF; /* Default */
                    color: #333333;
                }
                QPushButton:checked {
                    background-color: #A0D2EB; /* Softer blue */
                    border: 1px solid #7AB8E4;
                    color: #000000;
                }
                QPushButton:hover {
                    background-color: #EFEFEF;
                }
                QPushButton:checked:hover {
                    background-color: #8AC6E6;
                }
            """)
            if hour in self.selected_hours:
                btn.setChecked(True)
            btn.toggled.connect(lambda checked, h=hour: self.on_button_toggled(checked, h))

            row = index // col_count
            col = index % col_count
            self.layout.addWidget(btn, row, col)

    def on_button_toggled(self, checked, hour):
        if checked:
            self.selected_hours.add(hour)
        else:
            self.selected_hours.discard(hour)
        # Optional: Emit a signal when selection changes
        # self.selectionChanged.emit(self.getSelectedHours())

    def getSelectedHours(self):
        return sorted(list(self.selected_hours), key=lambda x: datetime.datetime.strptime(x, "%I:%M %p").time())

# ScheduleTimeItemWidget: Looks functional, add minor styling.
class ScheduleTimeItemWidget(QWidget):
    removed = pyqtSignal(QWidget) # Signal to notify parent when removed

    def __init__(self, parent=None):
        super().__init__(parent)
        # Modern card-like style for better visual grouping
        self.setStyleSheet("""
            ScheduleTimeItemWidget {
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                margin: 4px 2px;
                padding: 12px;
            }
            ScheduleTimeItemWidget:hover {
                background-color: #f8f9fa;
                border-color: #6c757d;
            }
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
                font-size: 11pt;
                min-width: 60px;
            }
            QComboBox:focus {
                border-color: #007bff;
                outline: none;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzZjNzU3ZCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }
            QPushButton {
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: 500;
            }
        """)
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(12)

        # Time section with label and icon
        timeContainer = QWidget()
        timeLayout = QHBoxLayout(timeContainer)
        timeLayout.setContentsMargins(0, 0, 0, 0)
        timeLayout.setSpacing(6)

        timeLabel = QLabel("🕐 Time:")
        timeLabel.setStyleSheet("font-weight: 600; color: #495057; margin-right: 5px;")
        timeLayout.addWidget(timeLabel)

        self.hourCombo = QComboBox()
        self.hourCombo.addItems([f"{h:02d}" for h in range(1, 13)])
        self.hourCombo.setToolTip("Hour")
        timeLayout.addWidget(self.hourCombo)

        colonLabel = QLabel(":")
        colonLabel.setStyleSheet("font-weight: bold; color: #6c757d; margin: 0 2px;")
        timeLayout.addWidget(colonLabel)

        self.minuteCombo = QComboBox()
        self.minuteCombo.addItems([f"{m:02d}" for m in range(0, 60, 5)]) # Steps of 5min default
        self.minuteCombo.setEditable(True) # Allow custom minutes
        self.minuteCombo.lineEdit().setValidator(QtGui.QIntValidator(0, 59)) # Validate input
        self.minuteCombo.setToolTip("Minutes")
        timeLayout.addWidget(self.minuteCombo)

        self.ampmCombo = QComboBox()
        self.ampmCombo.addItems(["AM", "PM"])
        self.ampmCombo.setToolTip("AM/PM")
        timeLayout.addWidget(self.ampmCombo)

        layout.addWidget(timeContainer)

        # Category section with label and icon
        categoryContainer = QWidget()
        categoryLayout = QHBoxLayout(categoryContainer)
        categoryLayout.setContentsMargins(0, 0, 0, 0)
        categoryLayout.setSpacing(6)

        categoryLabel = QLabel("📂 Category:")
        categoryLabel.setStyleSheet("font-weight: 600; color: #495057; margin-right: 5px;")
        categoryLayout.addWidget(categoryLabel)

        self.categoryCombo = QComboBox()
        self.categoryCombo.addItems(get_global_categories()) # Fetch categories dynamically
        self.categoryCombo.setToolTip("Post category")
        self.categoryCombo.setMinimumWidth(120)
        categoryLayout.addWidget(self.categoryCombo)

        layout.addWidget(categoryContainer)
        layout.addStretch(1) # Push button to the right

        # Modern remove button
        self.removeButton = QPushButton("✕")
        self.removeButton.setToolTip("Remove this time slot")
        self.removeButton.setFixedSize(QSize(32, 32))
        self.removeButton.setStyleSheet("""
            QPushButton {
                border: none;
                background-color: #dc3545;
                color: white;
                border-radius: 16px;
                font-weight: bold;
                font-size: 14pt;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        self.removeButton.setCursor(Qt.PointingHandCursor)

        layout.addWidget(self.removeButton)
        self.removeButton.clicked.connect(self.removeSelf)

    def removeSelf(self):
        self.removed.emit(self) # Emit signal before deletion
        self.deleteLater()

    def getData(self):
        try:
            hour = int(self.hourCombo.currentText())
            minute_text = self.minuteCombo.currentText()
            minute = int(minute_text) if minute_text.isdigit() else 0 # Default to 0 if invalid
            ampm = self.ampmCombo.currentText()

            # Ensure valid time components
            if not (1 <= hour <= 12 and 0 <= minute <= 59):
                raise ValueError("Invalid hour or minute")

            time_str = f"{hour:02d}:{minute:02d} {ampm}"
            category = self.categoryCombo.currentText()
            return {"time": time_str, "category": category}
        except ValueError as e:
            logging.warning(f"Invalid time data entered in ScheduleTimeItemWidget: {e}")
            QMessageBox.warning(self, "Invalid Time", "Please enter a valid time (HH:MM AM/PM).")
            return None # Indicate error

    def setData(self, time_str, category):
        """Sets the widget's values from data."""
        try:
            dt_obj = datetime.datetime.strptime(time_str, "%I:%M %p")
            self.hourCombo.setCurrentText(dt_obj.strftime("%I")) # %I is 12-hour 01-12
            self.minuteCombo.setCurrentText(dt_obj.strftime("%M"))
            self.ampmCombo.setCurrentText(dt_obj.strftime("%p"))
            self.categoryCombo.setCurrentText(category)
        except ValueError:
            logging.warning(f"Could not parse time string '{time_str}' to set ScheduleTimeItemWidget data.")

# StartDateDialog: Looks good.
class StartDateDialog(QDialog):
    # (Keep existing implementation - maybe minor styling)
     def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("📅 Schedule Configuration")
        self.resize(450, 350)
        # Apply enhanced styling
        self.setStyleSheet("""
            QDialog {
                background-color: #F8F9FA;
                border-radius: 8px;
            }
            QPushButton {
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QLabel {
                color: #2c3e50;
            }
            QSpinBox, QComboBox {
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }
        """)

        layout = QFormLayout(self)
        layout.setSpacing(12)

        # Add description
        desc_label = QLabel("Configure your posting schedule duration and start date:")
        desc_label.setStyleSheet("font-weight: bold; color: #2c3e50; margin-bottom: 10px; font-size: 12px;")
        layout.addRow(desc_label)

        self.monthCombo = QComboBox()
        months = [datetime.date(2000, m, 1).strftime('%B') for m in range(1, 13)]
        self.monthCombo.addItems(months)
        # Default to current month
        self.monthCombo.setCurrentIndex(datetime.date.today().month - 1)
        layout.addRow("Start Month:", self.monthCombo)

        self.yearSpin = QSpinBox()
        current_year = datetime.date.today().year
        self.yearSpin.setMinimum(current_year)
        self.yearSpin.setMaximum(current_year + 5) # Limit future years reasonably
        self.yearSpin.setValue(current_year)
        layout.addRow("Start Year:", self.yearSpin)

        # Duration selection
        self.durationSpin = QSpinBox()
        self.durationSpin.setRange(1, 365) # 1 day to 1 year
        self.durationSpin.setValue(30) # Default 30 days
        self.durationSpin.setSuffix(" days")
        layout.addRow("⏱️ Duration:", self.durationSpin)

        # Quick duration buttons
        duration_layout = QHBoxLayout()
        quick_durations = [
            ("1 Week", 7),
            ("2 Weeks", 14),
            ("1 Month", 30),
            ("2 Months", 60),
            ("3 Months", 90)
        ]

        for label, days in quick_durations:
            btn = QPushButton(label)
            btn.clicked.connect(lambda checked, d=days: self.durationSpin.setValue(d))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 3px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            duration_layout.addWidget(btn)

        layout.addRow("Quick Select:", duration_layout)

        # Info label
        self.infoLabel = QLabel()
        self.infoLabel.setStyleSheet("color: #7f8c8d; font-size: 11px;")
        self.updateInfoLabel()
        layout.addRow(self.infoLabel)

        # Connect signals to update info
        self.monthCombo.currentIndexChanged.connect(self.updateInfoLabel)
        self.yearSpin.valueChanged.connect(self.updateInfoLabel)
        self.durationSpin.valueChanged.connect(self.updateInfoLabel)

        # Enhanced OK/Cancel buttons
        buttonLayout = QHBoxLayout()
        self.okButton = QPushButton("✅ Create Schedule")
        self.okButton.setDefault(True) # Allow Enter key
        self.okButton.setCursor(Qt.PointingHandCursor)
        self.okButton.clicked.connect(self.accept)
        self.okButton.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        self.cancelButton = QPushButton("❌ Cancel")
        self.cancelButton.setCursor(Qt.PointingHandCursor)
        self.cancelButton.clicked.connect(self.reject)
        self.cancelButton.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        buttonLayout.addStretch()
        buttonLayout.addWidget(self.okButton)
        buttonLayout.addWidget(self.cancelButton)
        layout.addRow(buttonLayout) # Add row for buttons

     def updateInfoLabel(self):
        """Updates the info label with schedule details."""
        try:
            start_date = self.getStartDate()
            today = datetime.date.today()
            actual_start = max(start_date, today)
            duration = self.durationSpin.value()
            end_date = actual_start + datetime.timedelta(days=duration - 1)

            info_text = f"📅 Schedule: {actual_start.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"
            if actual_start > start_date:
                info_text += f"\n⚠️ Start date adjusted to today (no past dates)"

            self.infoLabel.setText(info_text)
        except:
            self.infoLabel.setText("📅 Schedule information")

     def getStartDate(self):
        """Returns the first day of the selected month and year."""
        month_index = self.monthCombo.currentIndex() + 1
        year = self.yearSpin.value()
        try:
            return datetime.date(year, month_index, 1)
        except ValueError:
            # Should not happen with combo/spinbox, but good practice
            logging.error(f"Invalid date created in StartDateDialog: {year}-{month_index}-01")
            # Fallback to current date
            return datetime.date.today().replace(day=1)

     def getNumDays(self):
        """Returns the selected duration in days."""
        return self.durationSpin.value()

# CreateScheduleDialog: Refine UI and logic. Fetch posts more efficiently.
class CreateScheduleDialog(QDialog):
    scheduleCreated = pyqtSignal(list) # Emit the created schedule list

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Create Monthly Schedule")
        # Consistent styling
        self.setStyleSheet("""
            QDialog { background-color: #FFFFFF; font-size: 11pt; }
            QLabel { margin-bottom: 4px; }
            QPushButton { padding: 8px 15px; margin-top: 10px; }
            QScrollArea { border: 1px solid #DCDCDC; }
            QSpinBox, QComboBox, QLineEdit { padding: 4px; }
        """)
        self.resize(650, 600) # Adjust size

        self.schedule_items = [] # To hold the ScheduleTimeItemWidget instances

        mainLayout = QVBoxLayout(self)
        mainLayout.setSpacing(15)

        # --- Top Settings ---
        settingsLayout = QGridLayout()
        settingsLayout.setSpacing(10)

        # Schedule Name
        settingsLayout.addWidget(QLabel("Schedule Name:"), 0, 0)
        self.scheduleNameEdit = QLineEdit()
        self.scheduleNameEdit.setPlaceholderText("Enter schedule name...")
        settingsLayout.addWidget(self.scheduleNameEdit, 0, 1, 1, 3)

        # Account Selection
        settingsLayout.addWidget(QLabel("Select Account:"), 1, 0)
        self.accountCombo = QComboBox()
        self.accountCombo.setPlaceholderText("Choose account...")
        self.accountCombo.currentTextChanged.connect(self.on_account_changed)
        settingsLayout.addWidget(self.accountCombo, 1, 1)

        # Posts per Day
        settingsLayout.addWidget(QLabel("Posts per Day:"), 1, 2)
        self.postsPerDaySpin = QSpinBox()
        self.postsPerDaySpin.setMinimum(1)
        self.postsPerDaySpin.setMaximum(24) # Sensible maximum
        self.postsPerDaySpin.setValue(1)
        self.postsPerDaySpin.valueChanged.connect(self.update_schedule_item_widgets)
        settingsLayout.addWidget(self.postsPerDaySpin, 1, 3)

        # Posting Mode Selection
        settingsLayout.addWidget(QLabel("Posting Mode:"), 2, 0)
        self.postingModeCombo = QComboBox()
        self.postingModeCombo.addItem("النشر التلقائي (Automatic Posting)", "automatic")
        self.postingModeCombo.addItem("النشر المباشر عبر NST Browser (Direct NST Browser)", "direct_nst")
        self.postingModeCombo.setCurrentIndex(1)  # Default to direct NST Browser
        settingsLayout.addWidget(self.postingModeCombo, 2, 1, 1, 3)

        # Page Selection (for pages managed by selected account)
        settingsLayout.addWidget(QLabel("Select Page (Optional):"), 3, 0)
        self.pageCombo = QComboBox()
        self.pageCombo.setPlaceholderText("Choose page or leave empty for profile...")
        self.pageCombo.setEnabled(False)
        settingsLayout.addWidget(self.pageCombo, 3, 1, 1, 2)

        # Group Selection (from pre-defined groups)
        settingsLayout.addWidget(QLabel("Select Groups:"), 4, 0)
        self.groupsListWidget = QListWidget()
        self.groupsListWidget.setMaximumHeight(100)
        self.groupsListWidget.setSelectionMode(QAbstractItemView.MultiSelection)
        self.groupsListWidget.setToolTip("Select groups where posts will be published")
        settingsLayout.addWidget(self.groupsListWidget, 4, 1, 1, 3)

        mainLayout.addLayout(settingsLayout)

        # Load initial data
        self.load_accounts_for_schedule()
        self.load_groups_for_schedule()

        # --- Schedule Items ---
        mainLayout.addWidget(QLabel("Define daily post slots (time and category):"))

        self.itemsContainerWidget = QWidget()
        self.itemsContainerLayout = QVBoxLayout(self.itemsContainerWidget)
        self.itemsContainerLayout.setContentsMargins(5, 5, 5, 5)
        self.itemsContainerLayout.setSpacing(5)
        # self.itemsContainerLayout.addStretch(1) # Add stretch at the bottom

        itemsScroll = QScrollArea()
        itemsScroll.setWidgetResizable(True)
        itemsScroll.setWidget(self.itemsContainerWidget)
        itemsScroll.setMinimumHeight(200) # Ensure it has some height
        mainLayout.addWidget(itemsScroll)

        # Initial population of schedule items
        self.update_schedule_item_widgets(self.postsPerDaySpin.value())

        # --- Buttons ---
        buttonLayout = QHBoxLayout()
        self.confirmBtn = QPushButton(QIcon.fromTheme("dialog-ok-apply"), "Generate Schedule")
        self.confirmBtn.setCursor(Qt.PointingHandCursor)
        # self.confirmBtn.setStyleSheet("background-color: #A0D2EB;")
        self.confirmBtn.clicked.connect(self.confirm_and_generate_schedule)
        self.confirmBtn.setDefault(True)

        self.cancelBtn = QPushButton(QIcon.fromTheme("dialog-cancel"), "Cancel")
        self.cancelBtn.setCursor(Qt.PointingHandCursor)
        # self.cancelBtn.setStyleSheet("background-color: #E0E0E0;")
        self.cancelBtn.clicked.connect(self.reject)

        buttonLayout.addStretch()
        buttonLayout.addWidget(self.confirmBtn)
        buttonLayout.addWidget(self.cancelBtn)
        mainLayout.addLayout(buttonLayout)

    def load_accounts_for_schedule(self):
        """Load available NST Browser accounts into the combo box."""
        self.accountCombo.clear()
        self.accountCombo.addItem("Select NST Browser Account...", None)

        try:
            # Get only NST Browser accounts from parent window
            if hasattr(self.parent(), 'all_accounts_data'):
                for account in self.parent().all_accounts_data:
                    # Only show accounts with NST Browser profiles
                    if account.nst_profile_id and account.browser_type == 'nst':
                        display_name = f"{account.full_name} (Profile: {account.nst_profile_id})" if account.full_name else f"NST Profile: {account.nst_profile_id}"
                        self.accountCombo.addItem(display_name, account)
        except Exception as e:
            logging.error(f"Error loading NST accounts for schedule: {e}")

    def load_groups_for_schedule(self):
        """Load groups for the selected account."""
        self.groupsListWidget.clear()

        current_account = self.accountCombo.currentData()
        if current_account is None:
            return

        try:
            # Get groups associated with the selected account
            groups = self.parent().get_groups_for_account(current_account.id)
            for group in groups:
                item = QListWidgetItem(f"{group['name']} (ID: {group['group_id']})")
                item.setData(Qt.UserRole, group)
                self.groupsListWidget.addItem(item)
        except Exception as e:
            logging.error(f"Error loading groups for account: {e}")

    def on_account_changed(self):
        """Handle account selection change."""
        self.pageCombo.clear()
        self.pageCombo.setEnabled(False)

        current_account = self.accountCombo.currentData()
        if current_account is None:
            self.groupsListWidget.clear()
            return

        # Enable page selection
        self.pageCombo.setEnabled(True)
        self.pageCombo.addItem("Post to Profile", None)

        # Load pages managed by this account
        try:
            pages = self.parent().get_pages_for_account(current_account.id)
            for page in pages:
                self.pageCombo.addItem(f"{page['name']} (ID: {page['page_id']})", page)
        except Exception as e:
            logging.error(f"Error loading pages for account: {e}")

        # Reload groups for this account
        self.load_groups_for_schedule()

    def get_selected_targets(self):
        """Get the selected posting targets."""
        targets = []

        # Get selected account
        selected_account = self.accountCombo.currentData()
        if selected_account is None:
            return targets

        # Get selected page (if any)
        selected_page = self.pageCombo.currentData()

        # Get selected groups
        selected_groups = []
        for i in range(self.groupsListWidget.count()):
            item = self.groupsListWidget.item(i)
            if item.isSelected():
                selected_groups.append(item.data(Qt.UserRole))

        # Build targets list
        if selected_page is None:
            # Post to profile
            targets.append({
                'type': 'profile',
                'account': selected_account,
                'target': None
            })
        else:
            # Post to page
            targets.append({
                'type': 'page',
                'account': selected_account,
                'target': selected_page
            })

        # Add groups
        for group in selected_groups:
            targets.append({
                'type': 'group',
                'account': selected_account,
                'target': group
            })

        return targets

    def update_schedule_item_widgets(self, count):
        """Add or remove ScheduleTimeItemWidget instances."""
        current_count = len(self.schedule_items)

        # Add widgets if needed
        if count > current_count:
            for _ in range(count - current_count):
                widget = ScheduleTimeItemWidget(self.itemsContainerWidget)
                widget.removed.connect(self.handle_item_removed) # Connect removal signal
                # Insert before the stretch if you have one
                self.itemsContainerLayout.insertWidget(self.itemsContainerLayout.count(), widget) # Add at the end
                self.schedule_items.append(widget)
        # Remove widgets if needed
        elif count < current_count:
            for _ in range(current_count - count):
                if self.schedule_items:
                    widget_to_remove = self.schedule_items.pop()
                    widget_to_remove.removed.disconnect(self.handle_item_removed) # Disconnect signal
                    widget_to_remove.deleteLater()

        # Adjust spinbox if removal happened via button
        if count != self.postsPerDaySpin.value():
             self.postsPerDaySpin.setValue(count)

    def handle_item_removed(self, widget):
        """Handle removal triggered by the item's own remove button."""
        if widget in self.schedule_items:
            self.schedule_items.remove(widget)
            # Update the spinbox to reflect the new count
            self.postsPerDaySpin.setValue(len(self.schedule_items))

    def confirm_and_generate_schedule(self):
        # Get selected targets from the new interface
        targets = self.get_selected_targets()

        if not targets:
            QMessageBox.warning(self, "Selection Required",
                              "Please select an account and at least one target (page or groups).")
            return

        # Get schedule name
        schedule_name = self.scheduleNameEdit.text().strip()
        if not schedule_name:
            QMessageBox.warning(self, "Input Required", "Please enter a schedule name.")
            self.scheduleNameEdit.setFocus()
            return

        schedule_definitions = []
        valid_definitions = True
        for item_widget in self.schedule_items:
            data = item_widget.getData()
            if data:
                schedule_definitions.append(data)
            else:
                valid_definitions = False # Found an invalid time entry
                break # Stop processing if one is invalid

        if not valid_definitions:
             QMessageBox.warning(self, "Invalid Time", "One or more time slots have invalid times. Please correct them.")
             return

        if not schedule_definitions:
            QMessageBox.warning(self, "Input Required", "Please define at least one valid post time slot.")
            return

        # Get start date and duration
        startDateDialog = StartDateDialog(self)
        if startDateDialog.exec_() != QDialog.Accepted:
            return # User cancelled date selection
        start_date = startDateDialog.getStartDate()
        num_days = startDateDialog.getNumDays()

        # Fetch available posts for each required category efficiently
        required_categories = list(set(d['category'] for d in schedule_definitions))
        available_posts = {}
        try:
            with QMutexLocker(db_mutex):
                for category in required_categories:
                    # Fetch only necessary data (id, content, image_path)
                    rows = c.execute(
                        "SELECT id, content, image_path FROM posts WHERE post_type=? AND status != 'failed' ORDER BY RANDOM()", # Randomize selection
                        (category,)
                    ).fetchall()
                    if rows:
                        available_posts[category] = [
                            {"post_id": row['id'], "content": row['content'], "image_path": row['image_path']}
                            for row in rows
                        ]
                    else:
                         available_posts[category] = []
                         logging.warning(f"No available posts found for category: {category}")

        except sqlite3.Error as e:
            logging.error(f"Database error fetching posts for schedule generation: {e}")
            QMessageBox.critical(self, "Database Error", f"Could not fetch posts: {e}")
            return

        # Check if any category has no posts
        missing_posts = [cat for cat in required_categories if not available_posts.get(cat)]
        if missing_posts:
            QMessageBox.warning(self, "Missing Posts",
                                f"No posts found for the following categories: {', '.join(missing_posts)}. "
                                "Schedule generation aborted.")
            return

        # Generate the schedule entries
        generated_schedule = []
        # Use a copy of the lists to allow reusing posts if needed, or track usage for uniqueness
        post_pools = {cat: list(posts) for cat, posts in available_posts.items()}
        post_indices = {cat: 0 for cat in post_pools}

        # Use today's date as the actual start date instead of the selected month's first day
        today = datetime.date.today()
        actual_start_date = max(start_date, today)  # Don't schedule posts in the past

        # Use the number of days selected by user

        total_entries = num_days * len(schedule_definitions) * len(targets)
        progress = QProgressDialog(f"Generating schedule for {num_days} days...", "Cancel", 0, total_entries, self)
        progress.setWindowModality(Qt.WindowModal)
        progress.setWindowTitle("📅 Creating Schedule")
        progress.setValue(0)

        # Show schedule info
        end_date = actual_start_date + datetime.timedelta(days=num_days - 1)
        logging.info(f"📅 Creating schedule from {actual_start_date} to {end_date} ({num_days} days)")

        QtWidgets.QApplication.processEvents() # Show the dialog

        schedule_possible = True
        current_progress = 0

        for day_offset in range(num_days):
            current_date = actual_start_date + datetime.timedelta(days=day_offset)
            # Sort definitions by time for chronological order if needed
            # sorted_definitions = sorted(schedule_definitions, key=lambda d: datetime.datetime.strptime(d['time'], "%I:%M %p").time())

            for definition in schedule_definitions: # Use original order or sorted
                if progress.wasCanceled():
                     schedule_possible = False
                     break

                category = definition['category']
                time_str = definition['time']

                # Select a post for this slot
                if category in post_pools and post_pools[category]:
                    pool = post_pools[category]
                    index = post_indices[category]

                    # Cycle through posts for the category
                    if index >= len(pool):
                        index = 0 # Reset index to reuse posts

                    selected_db_post = pool[index]
                    post_indices[category] = index + 1 # Move to next post for this category

                    # Create schedule entries for each target
                    for target in targets:
                        target_name = ""
                        target_type = target['type']

                        if target_type == 'profile':
                            target_name = target['account'].full_name or target['account'].fb_id
                        elif target_type == 'page':
                            target_name = target['target']['name']
                        elif target_type == 'group':
                            target_name = target['target']['name']

                        # Create the schedule entry dictionary
                        schedule_entry = {
                            "date": current_date.isoformat(),
                            "time": time_str,
                            "item_name": target_name,
                            "target_type": target_type,
                            "category": category,
                            "checked": False,
                            # Use the actual DB post ID, content, and images
                            "db_post_id": selected_db_post["post_id"],
                            "post_id": f"PID{selected_db_post['post_id'] + PID_OFFSET}",
                            "post_content": selected_db_post["content"],
                            "post_images": selected_db_post["image_path"],
                            # Add target information for posting
                            "account": target['account'],
                            "target_info": target['target'],
                            # Add posting mode information
                            "posting_mode": self.postingModeCombo.currentData()
                        }
                        generated_schedule.append(schedule_entry)
                else:
                    # This case should be prevented by the check above, but handle defensively
                    logging.error(f"Attempted to schedule for category '{category}' but no posts available or pool empty.")
                    # Decide behaviour: skip slot, abort, etc.
                    # For now, just log and continue, the schedule will be incomplete.
                    pass # Or set schedule_possible = False

                current_progress += 1
                progress.setValue(current_progress)
                QtWidgets.QApplication.processEvents() # Keep UI responsive

            if not schedule_possible: # Break outer loop if cancelled
                 break

        progress.close() # Close progress dialog

        if not schedule_possible:
             logging.info("Schedule generation cancelled by user.")
             return # Don't proceed if cancelled

        if not generated_schedule:
            QMessageBox.information(self, "Schedule Not Created",
                                    "No schedule entries were generated. This might be due to missing posts or cancellation.")
            return

        # Show success message with details
        start_date_str = actual_start_date.strftime('%Y-%m-%d')
        end_date_str = (actual_start_date + datetime.timedelta(days=num_days - 1)).strftime('%Y-%m-%d')

        # Get posting mode for message
        posting_mode = self.postingModeCombo.currentData()
        posting_mode_text = "النشر المباشر عبر NST Browser" if posting_mode == "direct_nst" else "النشر التلقائي"

        success_msg = f"""✅ Schedule Created Successfully!

📊 Summary:
• Total Entries: {len(generated_schedule)}
• Duration: {num_days} days
• Period: {start_date_str} to {end_date_str}
• Targets: {len(targets)} accounts/pages/groups
• Time Slots: {len(schedule_definitions)} per day
• Posting Mode: {posting_mode_text}

💡 Next Steps:
1. Review the schedule in the new tab
2. Select entries you want to publish (✓)"""

        if posting_mode == "direct_nst":
            success_msg += """
3. Click "🚀 Open NST Browser for Direct Posting" to post directly
4. Browser will open for each selected entry for manual posting"""
        else:
            success_msg += """
3. Click "📅 Create Scheduled Posts" to activate them
4. Enable auto-posting to publish at scheduled times"""

        QMessageBox.information(self, "🎉 Schedule Created", success_msg)

        logging.info(f"✅ Generated {len(generated_schedule)} schedule entries from {start_date_str} to {end_date_str}")
        self.scheduleCreated.emit(generated_schedule) # Emit the result
        self.accept() # Close the dialog


# --- Table Widgets & Headers ---

# CheckBoxHeader class removed - now using standard header with separate Select All/Deselect All buttons


class PostsTableWidget(QTableWidget):
    """Specialized table for displaying posts with checkboxes and thumbnails."""
    # Define signals if needed, e.g., editRequest = pyqtSignal(int)

    # Define column indices as constants for clarity
    COL_PID = 0
    # Columns vary by type, handle dynamically or use a config

    def __init__(self, post_type, parent=None):
        super().__init__(parent)
        self.post_type = post_type
        self.post_columns = self._get_columns_for_type(post_type)

        self.setColumnCount(len(self.post_columns))
        self.setHorizontalHeaderLabels([col['label'] for col in self.post_columns])

        # Use standard header without checkbox
        header = self.horizontalHeader()
        header.setSectionsClickable(True)

        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.SingleSelection) # Or ExtendedSelection if multi-edit is needed
        self.setEditTriggers(QAbstractItemView.NoEditTriggers) # Don't allow editing directly in table
        self.setAlternatingRowColors(True) # Improve readability
        self.setSortingEnabled(True) # Allow sorting
        self.verticalHeader().setVisible(False) # Hide row numbers

        # Optimize column resizing
        header = self.horizontalHeader()
        # Stretch most columns, but give specific columns fixed/interactive widths
        for i, col_info in enumerate(self.post_columns):
             resize_mode = col_info.get('resize', QHeaderView.Stretch)
             header.setSectionResizeMode(i, resize_mode)
             if 'width' in col_info:
                 header.resizeSection(i, col_info['width']) # Set initial width

        # Connect double click for editing (optional)
        # self.itemDoubleClicked.connect(self.handle_double_click)

    def _get_columns_for_type(self, post_type):
        # Define column structure, labels, resizing, widths, data keys
        # Last column is the 'Select' checkbox column
        common_cols_start = [
            {'label': "Post ID", 'key': 'display_pid', 'resize': QHeaderView.Interactive, 'width': 100},
        ]
        common_cols_end = [
            {'label': "Type", 'key': 'post_type', 'resize': QHeaderView.Interactive, 'width': 80},
            {'label': "Status", 'key': 'status', 'resize': QHeaderView.Interactive, 'width': 80},
            {'label': "Select", 'key': 'checkbox', 'resize': QHeaderView.Fixed, 'width': 50}
        ]

        if post_type == "Recipes":
            return common_cols_start + [
                {'label': "FB Post", 'key': 'content', 'resize': QHeaderView.Stretch},
                {'label': "Recipe", 'key': 'recipe_text', 'resize': QHeaderView.Stretch},
                {'label': "Website", 'key': 'website_link', 'resize': QHeaderView.Interactive, 'width': 150},
                {'label': "Images", 'key': 'images', 'resize': QHeaderView.Interactive, 'width': 120},
            ] + common_cols_end
        elif post_type == "Engage":
            return common_cols_start + [
                {'label': "Images", 'key': 'images', 'resize': QHeaderView.Interactive, 'width': 120},
                {'label': "Content", 'key': 'content', 'resize': QHeaderView.Stretch},
            ] + common_cols_end
        elif post_type == "Parole":
            return common_cols_start + [
                {'label': "Content", 'key': 'content', 'resize': QHeaderView.Stretch},
            ] + common_cols_end
        else:
            return common_cols_start + common_cols_end # Default fallback

    def select_all_posts(self, checked=True):
        """Select or deselect all posts in this table."""
        for row in range(self.rowCount()):
            widget = self.cellWidget(row, self.columnCount() - 1) # Checkbox is last column
            if widget and isinstance(widget, QWidget): # Check container exists
                checkbox = widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(checked)

    def populate_table(self, posts: list[PostData]):
        """Clears and fills the table with PostData objects."""
        self.setUpdatesEnabled(False) # Disable updates for performance
        self.setSortingEnabled(False) # Disable sorting during population
        self.clearContents()
        self.setRowCount(len(posts))

        status_colors = {
            "posted": QColor("#DFF0D8"), # Light green
            "pending": QColor("#FCF8E3"), # Light yellow
            "failed": QColor("#F2DEDE"), # Light red
            "live": QColor("#DFF0D8"), # Treat live like posted
        }
        default_color = QColor(Qt.white)

        col_map = {col_info['key']: i for i, col_info in enumerate(self.post_columns)}

        for row_idx, post in enumerate(posts):
            # --- Populate Data Cells ---
            for col_key, col_idx in col_map.items():
                if col_key == 'checkbox': continue # Handled separately
                if col_key == 'images': continue # Handled separately

                value = getattr(post, col_key, "") # Get value from PostData object
                if value is None: value = ""

                # Truncate long text for display
                if col_key in ['content', 'recipe_text'] and len(str(value)) > 100:
                    display_value = str(value)[:100] + "..."
                else:
                    display_value = str(value)

                item = QTableWidgetItem(display_value)
                item.setData(Qt.UserRole, post.id) # Store DB ID in item data

                 # Set tooltip for long text
                if display_value != str(value):
                     item.setToolTip(str(value))

                # Alignment (optional)
                if col_key == 'display_pid':
                    item.setTextAlignment(Qt.AlignCenter)
                elif col_key in ['post_type', 'status']:
                     item.setTextAlignment(Qt.AlignCenter)


                self.setItem(row_idx, col_idx, item)

            # --- Image Thumbnails ---
            if 'images' in col_map:
                images_col_idx = col_map['images']
                image_paths = post.image_list
                if image_paths:
                    thumb_widget = QWidget()
                    thumb_layout = QHBoxLayout(thumb_widget)
                    thumb_layout.setContentsMargins(2, 2, 2, 2)
                    thumb_layout.setSpacing(3)
                    thumb_layout.setAlignment(Qt.AlignCenter) # Center thumbnails

                    # Limit number of thumbnails shown for performance
                    max_thumbs = 3
                    for i, img_path in enumerate(image_paths):
                         if i >= max_thumbs:
                              # Add label indicating more images
                              more_label = QLabel(f"+{len(image_paths) - max_thumbs}")
                              more_label.setToolTip(f"{len(image_paths)} total images")
                              thumb_layout.addWidget(more_label)
                              break

                         pixmap = create_thumbnail(img_path, SMALL_THUMBNAIL_SIZE)
                         if not pixmap.isNull():
                            thumb_label = QLabel()
                            thumb_label.setPixmap(pixmap)
                            thumb_label.setToolTip(os.path.basename(img_path))
                            # Optionally make clickable: thumb_label.mousePressEvent = lambda e, p=img_path: self.parent().showPreview(p)
                            thumb_layout.addWidget(thumb_label)

                    if thumb_layout.count() == 0: # If no valid images found
                         thumb_layout.addWidget(QLabel("N/A"))

                    self.setCellWidget(row_idx, images_col_idx, thumb_widget)
                    # Adjust row height slightly if thumbnails are present
                    self.setRowHeight(row_idx, 40)
                else:
                    item = QTableWidgetItem("N/A")
                    item.setTextAlignment(Qt.AlignCenter)
                    self.setItem(row_idx, images_col_idx, item)


            # --- Checkbox ---
            checkbox_col_idx = col_map['checkbox']
            check = QCheckBox()
            # Store post ID in checkbox property for easy retrieval
            check.setProperty("post_id", post.id)

            check_container = QWidget() # Use container for centering
            check_layout = QHBoxLayout(check_container)
            check_layout.addWidget(check)
            check_layout.setAlignment(Qt.AlignCenter)
            check_layout.setContentsMargins(0, 0, 0, 0)
            self.setCellWidget(row_idx, checkbox_col_idx, check_container)

            # --- Row Color Based on Status ---
            row_color = status_colors.get(post.status.lower(), default_color)
            for col_idx in range(self.columnCount()):
                if self.item(row_idx, col_idx):
                    self.item(row_idx, col_idx).setBackground(row_color)
                if self.cellWidget(row_idx, col_idx):
                     # Apply to container widgets too
                     self.cellWidget(row_idx, col_idx).setStyleSheet(f"background-color: {row_color.name()};")


        self.setSortingEnabled(True) # Re-enable sorting
        self.setUpdatesEnabled(True) # Re-enable updates

    def get_selected_post_ids(self) -> list[int]:
        """Returns a list of database IDs for rows where the checkbox is checked."""
        selected_ids = []
        for row in range(self.rowCount()):
            widget = self.cellWidget(row, self.columnCount() - 1) # Checkbox is last column
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    post_id = checkbox.property("post_id")
                    if post_id is not None:
                        selected_ids.append(post_id)
        return selected_ids

# ThumbnailLabel and LargePreviewLabel seem okay, maybe add minor styling/tooltips.
class ThumbnailLabel(QLabel):
    # Add a signal to request showing a larger preview
    imageClicked = pyqtSignal(str)

    def __init__(self, imagePath, size=SMALL_THUMBNAIL_SIZE, parent=None):
        super().__init__(parent)
        self.imagePath = imagePath
        self.setFixedSize(size[0], size[1])
        self.setScaledContents(False) # Keep aspect ratio better
        self.setAlignment(Qt.AlignCenter)

        pixmap = create_thumbnail(imagePath, size)
        if not pixmap.isNull():
            self.setPixmap(pixmap)
            self.setToolTip(f"{os.path.basename(imagePath)}\nClick to view larger")
        else:
            self.setText("ERR") # Indicate loading error
            self.setToolTip(f"Error loading image:\n{imagePath}")

        self.setCursor(Qt.PointingHandCursor)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton and self.imagePath:
            self.imageClicked.emit(self.imagePath) # Emit signal on click
        super().mousePressEvent(event)


class ImagePreviewDialog(QDialog):
    """Simple dialog to show a larger image."""
    def __init__(self, image_path, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"Preview - {os.path.basename(image_path)}")

        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.image_label)

        layout = QVBoxLayout(self)
        layout.addWidget(scroll_area)

        # Load full image
        pixmap = QPixmap(image_path)
        if pixmap.isNull():
            self.image_label.setText(f"Could not load image:\n{image_path}")
            self.resize(300, 200)
        else:
             # Scale down if image is very large to fit screen initially
            screen_geometry = QApplication.primaryScreen().availableGeometry()
            max_width = screen_geometry.width() * 0.8
            max_height = screen_geometry.height() * 0.8
            if pixmap.width() > max_width or pixmap.height() > max_height:
                pixmap = pixmap.scaled(int(max_width), int(max_height), Qt.KeepAspectRatio, Qt.SmoothTransformation)

            self.image_label.setPixmap(pixmap)
             # Resize dialog to fit image, up to screen limits
            self.resize(min(pixmap.width() + 40, int(max_width)+40),
                        min(pixmap.height() + 40, int(max_height)+40) )


# CreatePostDialog & EditPostDialog: Streamline, improve image handling UI.
class PostDialogBase(QDialog):
    """Base class for Create/Edit Post dialogs."""
    postSaved = pyqtSignal(object) # Emit PostData object or dict

    def __init__(self, post_type, parent=None, post_data: PostData | None = None):
        super().__init__(parent)
        self.post_type = post_type
        self.post_data = post_data if post_data else PostData(post_type=post_type) # Work with PostData
        self.is_edit_mode = post_data is not None

        self.setWindowTitle(f"{'Edit' if self.is_edit_mode else 'Create'} Post - {post_type}")
        self.setMinimumWidth(550)
        # Apply consistent styling
        self.setStyleSheet("""
            QDialog { background-color: #FFFFFF; font-size: 11pt; }
            QLabel { margin-bottom: 3px; font-weight: bold; }
            QLineEdit, QTextEdit, QComboBox { padding: 5px; border: 1px solid #CCCCCC; border-radius: 3px; }
            QTextEdit { min-height: 80px; }
            QPushButton { padding: 8px 15px; margin-top: 10px; }
            #imageScrollArea { border: 1px dashed #AAAAAA; background-color: #F9F9F9; }
        """)

        mainLayout = QVBoxLayout(self)
        formLayout = QFormLayout()
        formLayout.setSpacing(10)
        formLayout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow) # Allow fields to expand

        # --- Fields based on Post Type ---
        self.contentEdit = QTextEdit()
        formLayout.addRow(f"{post_type} Content:", self.contentEdit)
        self.contentEdit.setPlainText(self.post_data.content or "")

        if post_type == "Recipes":
            self.recipeEdit = QTextEdit()
            formLayout.addRow("Full Recipe:", self.recipeEdit)
            self.recipeEdit.setPlainText(self.post_data.recipe_text or "")

            self.websiteEdit = QLineEdit()
            self.websiteEdit.setPlaceholderText("https://example.com/recipe")
            formLayout.addRow("Website Link:", self.websiteEdit)
            self.websiteEdit.setText(self.post_data.website_link or "")

        mainLayout.addLayout(formLayout)

        # --- Image Handling ---
        imageGroupBox = QtWidgets.QGroupBox("Images")
        imageLayout = QVBoxLayout(imageGroupBox)

        self.imagePaths = self.post_data.image_list[:] # Work with a copy

        # Use a flow layout for thumbnails
        self.thumbnailsContainer = QWidget()
        self.thumbnailsFlowLayout = FlowLayout(spacing=5) # Use FlowLayout (implementation below)
        self.thumbnailsContainer.setLayout(self.thumbnailsFlowLayout)

        thumbnailsScroll = QScrollArea()
        thumbnailsScroll.setObjectName("imageScrollArea") # For styling
        thumbnailsScroll.setWidgetResizable(True)
        thumbnailsScroll.setWidget(self.thumbnailsContainer)
        thumbnailsScroll.setMinimumHeight(120)
        thumbnailsScroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        imageButtonsLayout = QHBoxLayout()
        self.addImagesBtn = QPushButton(QIcon.fromTheme("list-add"), "Add Images")
        self.addImagesBtn.setToolTip("Add one or more images")
        self.addImagesBtn.clicked.connect(self.add_images)
        imageButtonsLayout.addWidget(self.addImagesBtn)
        imageButtonsLayout.addStretch()

        if post_type == "Engage":
            self.addImagesBtn.setText("Add Image")
            self.addImagesBtn.setIcon(QIcon.fromTheme("list-add"))
            self.addImagesBtn.setToolTip("Add a single image for Engage post")
            # Optionally limit selection to 1 image later in add_images

        imageLayout.addLayout(imageButtonsLayout)
        imageLayout.addWidget(thumbnailsScroll)
        mainLayout.addWidget(imageGroupBox)

        # --- Action Buttons ---
        buttonLayout = QHBoxLayout()
        self.saveButton = QPushButton(QIcon.fromTheme("document-save"), "Save Post")
        self.saveButton.setDefault(True)
        self.saveButton.clicked.connect(self.save_post)

        self.cancelButton = QPushButton(QIcon.fromTheme("dialog-cancel"), "Cancel")
        self.cancelButton.clicked.connect(self.reject)

        buttonLayout.addStretch()
        buttonLayout.addWidget(self.saveButton)
        buttonLayout.addWidget(self.cancelButton)
        mainLayout.addLayout(buttonLayout)

        self.update_thumbnails_ui() # Initial population

    def add_images(self):
        """Opens file dialog to add images."""
        max_images = 1 if self.post_type == "Engage" else 0 # 0 means unlimited for others
        current_count = len(self.imagePaths)

        if max_images > 0 and current_count >= max_images:
             QMessageBox.information(self, "Image Limit", f"Only {max_images} image allowed for {self.post_type} posts.")
             return

        dialog = QFileDialog(self, "Select Images", "", "Images (*.png *.jpg *.jpeg *.bmp *.gif)")
        if max_images == 1:
             dialog.setFileMode(QFileDialog.ExistingFile)
        else:
             dialog.setFileMode(QFileDialog.ExistingFiles)

        if dialog.exec_():
            new_paths = dialog.selectedFiles()
            added_count = 0
            for path in new_paths:
                 if path not in self.imagePaths:
                      if max_images > 0 and len(self.imagePaths) >= max_images:
                           break # Stop if limit reached mid-selection
                      self.imagePaths.append(path)
                      added_count += 1

            if added_count > 0:
                 self.update_thumbnails_ui()
            if max_images > 0 and len(self.imagePaths) > max_images:
                 QMessageBox.warning(self, "Image Limit Exceeded", f"Selection limit is {max_images}. Excess images were ignored.")


    def update_thumbnails_ui(self):
        """Clears and repopulates the thumbnail area."""
        clearLayout(self.thumbnailsFlowLayout) # Clear existing thumbnails

        if not self.imagePaths:
             # Show placeholder text
             placeholder = QLabel("Click 'Add Images' to upload.")
             placeholder.setStyleSheet("font-style: italic; color: grey;")
             placeholder.setAlignment(Qt.AlignCenter)
             self.thumbnailsFlowLayout.addWidget(placeholder)
             return

        for path in self.imagePaths:
            if os.path.exists(path):
                thumb_widget = ImageThumbnailWidget(path) # Use dedicated widget
                thumb_widget.removeRequested.connect(self.remove_image)
                thumb_widget.viewRequested.connect(self.view_image)
                self.thumbnailsFlowLayout.addWidget(thumb_widget)
            else:
                logging.warning(f"Image path in list does not exist: {path}")
                # Optionally remove invalid paths from self.imagePaths here

    def remove_image(self, image_path):
        """Removes an image path and updates the UI."""
        if image_path in self.imagePaths:
            self.imagePaths.remove(image_path)
            self.update_thumbnails_ui()

    def view_image(self, image_path):
         """Shows a larger preview of the image."""
         preview_dialog = ImagePreviewDialog(image_path, self)
         preview_dialog.exec_()


    def save_post(self):
        """Validates input and emits the post data."""
        # Update PostData object
        self.post_data.content = self.contentEdit.toPlainText().strip()

        # --- Validation ---
        if not self.post_data.content:
            QMessageBox.warning(self, "Input Required", f"{self.post_type} content cannot be empty.")
            self.contentEdit.setFocus()
            return

        if self.post_type == "Recipes":
            self.post_data.recipe_text = self.recipeEdit.toPlainText().strip()
            self.post_data.website_link = self.websiteEdit.text().strip()
            if not self.post_data.recipe_text:
                QMessageBox.warning(self, "Input Required", "Full Recipe content cannot be empty.")
                self.recipeEdit.setFocus()
                return
            if not self.post_data.website_link:
                QMessageBox.warning(self, "Input Required", "Website Link cannot be empty.")
                self.websiteEdit.setFocus()
                return
            # Basic URL validation (optional)
            if not self.post_data.website_link.startswith(('http://', 'https://')):
                 QMessageBox.warning(self, "Invalid Link", "Website Link must start with http:// or https://.")
                 self.websiteEdit.setFocus()
                 return

        # Update image path (store as comma-separated string)
        self.post_data.image_path = ",".join(self.imagePaths)

        # Emit the updated post_data object
        self.postSaved.emit(self.post_data)
        self.accept() # Close dialog


# --- Helper Widgets for PostDialog ---

class FlowLayout(QtWidgets.QLayout):
    """A layout that arranges items like text flow."""
    # Standard FlowLayout implementation (find online or keep if you have one)
    # Source: PyQt examples or search "PyQt FlowLayout"
    def __init__(self, parent=None, spacing=-1):
        super().__init__(parent)
        if parent is not None:
            self.setContentsMargins(0, 0, 0, 0)
        self._spacing = spacing
        self._item_list = []

    def __del__(self):
        item = self.takeAt(0)
        while item:
            item = self.takeAt(0)

    def addItem(self, item):
        self._item_list.append(item)

    def count(self):
        return len(self._item_list)

    def itemAt(self, index):
        if 0 <= index < len(self._item_list):
            return self._item_list[index]
        return None

    def takeAt(self, index):
        if 0 <= index < len(self._item_list):
            return self._item_list.pop(index)
        return None

    def expandingDirections(self):
        return Qt.Orientations(Qt.Orientation(0)) # Not expanding

    def hasHeightForWidth(self):
        return True

    def heightForWidth(self, width):
        return self._do_layout(QtCore.QRect(0, 0, width, 0), True)

    def setGeometry(self, rect):
        super().setGeometry(rect)
        self._do_layout(rect, False)

    def sizeHint(self):
        return self.minimumSize()

    def minimumSize(self):
        size = QtCore.QSize()
        for item in self._item_list:
            size = size.expandedTo(item.minimumSize())
        margin, _, _, _ = self.getContentsMargins()
        size += QtCore.QSize(2 * margin, 2 * margin)
        return size

    def spacing(self):
        if self._spacing >= 0:
            return self._spacing
        else:
            return self.style().pixelMetric(QtWidgets.QStyle.PM_LayoutHorizontalSpacing)

    def _do_layout(self, rect, test_only):
        x = rect.x()
        y = rect.y()
        line_height = 0
        spacing = self.spacing()

        for item in self._item_list:
            next_x = x + item.sizeHint().width() + spacing
            if next_x - spacing > rect.right() and line_height > 0:
                x = rect.x()
                y = y + line_height + spacing
                line_height = 0

            if not test_only:
                item.setGeometry(QtCore.QRect(QtCore.QPoint(x, y), item.sizeHint()))

            x = next_x
            line_height = max(line_height, item.sizeHint().height())

        return y + line_height - rect.y()


class ImageThumbnailWidget(QFrame):
    """Widget to display an image thumbnail with remove/view buttons."""
    removeRequested = pyqtSignal(str)
    viewRequested = pyqtSignal(str)

    def __init__(self, image_path, size=DEFAULT_THUMBNAIL_SIZE, parent=None):
        super().__init__(parent)
        self.image_path = image_path
        self.setFixedSize(size[0] + 10, size[1] + 10) # Add padding for buttons/border
        self.setFrameShape(QFrame.Box)
        self.setFrameShadow(QFrame.Sunken)
        # self.setStyleSheet("ImageThumbnailWidget { border: 1px solid #CCCCCC; border-radius: 3px; }")

        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)

        # --- Thumbnail Label ---
        self.thumb_label = QLabel()
        self.thumb_label.setAlignment(Qt.AlignCenter)
        pixmap = create_thumbnail(image_path, size)
        if not pixmap.isNull():
            self.thumb_label.setPixmap(pixmap)
        else:
            self.thumb_label.setText("Error")
        layout.addWidget(self.thumb_label, 1) # Give label more space

        # --- Button Bar ---
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(3)

        view_btn = QPushButton(QIcon.fromTheme("zoom-in"), "")
        view_btn.setToolTip("View larger image")
        view_btn.setFlat(True)
        view_btn.setFixedSize(18, 18)
        view_btn.setIconSize(QSize(16, 16))
        view_btn.clicked.connect(lambda: self.viewRequested.emit(self.image_path))
        button_layout.addWidget(view_btn)

        remove_btn = QPushButton(QIcon.fromTheme("edit-delete"), "")
        remove_btn.setToolTip("Remove this image")
        remove_btn.setFlat(True)
        remove_btn.setFixedSize(18, 18)
        remove_btn.setIconSize(QSize(16, 16))
        remove_btn.clicked.connect(lambda: self.removeRequested.emit(self.image_path))
        button_layout.addWidget(remove_btn)

        layout.addLayout(button_layout)


# EditAccountDialog: Seems okay.
class EditAccountDialog(QDialog):
    # (Keep existing implementation - maybe minor styling)
     def __init__(self, account_data: AccountData, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Edit Account")
        self.account_data = account_data # Store the AccountData object

        # Basic Styling
        self.setStyleSheet("QDialog { background-color: #F5F5F5; } QPushButton { padding: 6px 12px; }")

        layout = QFormLayout(self)
        layout.setSpacing(10)

        self.fbIdLabel = QLabel(self.account_data.fb_id or "N/A")
        layout.addRow("Facebook UID:", self.fbIdLabel)

        self.fullNameEdit = QLineEdit(self.account_data.full_name or "")
        self.fullNameEdit.setPlaceholderText("Enter account's full name")
        layout.addRow("Full Name:", self.fullNameEdit)

        self.statusCombo = QComboBox() # Use ComboBox for defined statuses
        self.statusCombo.addItems(["live", "restricted", "blocked", "unknown"])
        self.statusCombo.setCurrentText(self.account_data.status or "unknown")
        layout.addRow("Status:", self.statusCombo)

        # Buttons
        buttonLayout = QHBoxLayout()
        self.saveBtn = QPushButton(QIcon.fromTheme("document-save"), "Save")
        self.saveBtn.setDefault(True)
        self.saveBtn.setCursor(Qt.PointingHandCursor)
        self.saveBtn.clicked.connect(self.accept)

        self.cancelBtn = QPushButton(QIcon.fromTheme("dialog-cancel"), "Cancel")
        self.cancelBtn.setCursor(Qt.PointingHandCursor)
        self.cancelBtn.clicked.connect(self.reject)

        buttonLayout.addStretch()
        buttonLayout.addWidget(self.saveBtn)
        buttonLayout.addWidget(self.cancelBtn)
        layout.addRow(buttonLayout)

     def getUpdatedData(self) -> tuple[str, str]:
        """Returns the updated full name and status."""
        new_name = self.fullNameEdit.text().strip()
        new_status = self.statusCombo.currentText()
        return new_name, new_status



# --- Manage Pages/Groups Dialog ---

class ManagePagesGroupsDialog(QDialog):
    """Dialog for managing pages and groups for a specific NST Browser profile."""

    def __init__(self, parent, profile_id, account_id):
        super().__init__(parent)
        self.parent = parent
        self.profile_id = profile_id
        self.account_id = account_id

        self.setWindowTitle(f"Manage Pages/Groups - Profile: {profile_id}")
        self.setModal(True)
        self.resize(800, 600)

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout(self)

        # Header
        header = QLabel(f"Manage Pages and Groups for NST Profile: {self.profile_id}")
        header.setStyleSheet("font-size: 14px; font-weight: bold; color: #2196F3; margin-bottom: 10px;")
        layout.addWidget(header)

        # Tab widget for pages and groups
        self.tabWidget = QTabWidget()

        # Pages tab
        self.pages_tab = self.create_pages_tab()
        self.tabWidget.addTab(self.pages_tab, "Facebook Pages")

        # Groups tab
        self.groups_tab = self.create_groups_tab()
        self.tabWidget.addTab(self.groups_tab, "Facebook Groups")

        layout.addWidget(self.tabWidget)

        # Buttons
        button_layout = QHBoxLayout()

        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self.load_data)
        button_layout.addWidget(refresh_btn)

        button_layout.addStretch()

        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

    def create_pages_tab(self):
        """Create the pages management tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Add pages section
        add_section = QGroupBox("Add New Page")
        add_layout = QGridLayout(add_section)

        add_layout.addWidget(QLabel("Page Name:"), 0, 0)
        self.page_name_edit = QLineEdit()
        self.page_name_edit.setPlaceholderText("Enter page name")
        add_layout.addWidget(self.page_name_edit, 0, 1)

        add_layout.addWidget(QLabel("Page ID:"), 1, 0)
        self.page_id_edit = QLineEdit()
        self.page_id_edit.setPlaceholderText("Enter Facebook page ID")
        add_layout.addWidget(self.page_id_edit, 1, 1)

        add_layout.addWidget(QLabel("Page URL (Optional):"), 2, 0)
        self.page_url_edit = QLineEdit()
        self.page_url_edit.setPlaceholderText("Enter page URL (optional)")
        add_layout.addWidget(self.page_url_edit, 2, 1)

        add_page_btn = QPushButton("Add Page")
        add_page_btn.clicked.connect(self.add_page)
        add_page_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        add_layout.addWidget(add_page_btn, 3, 0, 1, 2)

        layout.addWidget(add_section)

        # Pages list
        list_section = QGroupBox("Current Pages")
        list_layout = QVBoxLayout(list_section)

        self.pages_table = QTableWidget()
        self.pages_table.setColumnCount(4)
        self.pages_table.setHorizontalHeaderLabels(["Page Name", "Page ID", "URL", "Actions"])

        header = self.pages_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.Interactive)
        header.setSectionResizeMode(2, QHeaderView.Interactive)
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        header.resizeSection(3, 100)

        list_layout.addWidget(self.pages_table)
        layout.addWidget(list_section)

        return widget

    def create_groups_tab(self):
        """Create the groups management tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Add groups section
        add_section = QGroupBox("Add New Group")
        add_layout = QGridLayout(add_section)

        add_layout.addWidget(QLabel("Group Name:"), 0, 0)
        self.group_name_edit = QLineEdit()
        self.group_name_edit.setPlaceholderText("Enter group name")
        add_layout.addWidget(self.group_name_edit, 0, 1)

        add_layout.addWidget(QLabel("Group ID:"), 1, 0)
        self.group_id_edit = QLineEdit()
        self.group_id_edit.setPlaceholderText("Enter Facebook group ID")
        add_layout.addWidget(self.group_id_edit, 1, 1)

        add_layout.addWidget(QLabel("Group URL (Optional):"), 2, 0)
        self.group_url_edit = QLineEdit()
        self.group_url_edit.setPlaceholderText("Enter group URL (optional)")
        add_layout.addWidget(self.group_url_edit, 2, 1)

        add_group_btn = QPushButton("Add Group")
        add_group_btn.clicked.connect(self.add_group)
        add_group_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        add_layout.addWidget(add_group_btn, 3, 0, 1, 2)

        layout.addWidget(add_section)

        # Groups list
        list_section = QGroupBox("Current Groups")
        list_layout = QVBoxLayout(list_section)

        self.groups_table = QTableWidget()
        self.groups_table.setColumnCount(4)
        self.groups_table.setHorizontalHeaderLabels(["Group Name", "Group ID", "URL", "Actions"])

        header = self.groups_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.Interactive)
        header.setSectionResizeMode(2, QHeaderView.Interactive)
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        header.resizeSection(3, 100)

        list_layout.addWidget(self.groups_table)
        layout.addWidget(list_section)

        return widget

    def load_data(self):
        """Load pages and groups data for this account."""
        self.load_pages()
        self.load_groups()

    def load_pages(self):
        """Load pages for this account."""
        pages = self.parent.get_pages_for_account(self.account_id)

        self.pages_table.setRowCount(len(pages))
        for row, page in enumerate(pages):
            self.pages_table.setItem(row, 0, QTableWidgetItem(page['name']))
            self.pages_table.setItem(row, 1, QTableWidgetItem(page['page_id']))
            self.pages_table.setItem(row, 2, QTableWidgetItem(page['url']))

            # Delete button
            delete_btn = QPushButton("Delete")
            delete_btn.setStyleSheet("background-color: #F44336; color: white; font-weight: bold;")
            delete_btn.clicked.connect(lambda checked, page_id=page['id']: self.delete_page(page_id))
            self.pages_table.setCellWidget(row, 3, delete_btn)

    def load_groups(self):
        """Load groups for this account."""
        groups = self.parent.get_groups_for_account(self.account_id)

        self.groups_table.setRowCount(len(groups))
        for row, group in enumerate(groups):
            self.groups_table.setItem(row, 0, QTableWidgetItem(group['name']))
            self.groups_table.setItem(row, 1, QTableWidgetItem(group['group_id']))
            self.groups_table.setItem(row, 2, QTableWidgetItem(group['url']))

            # Delete button
            delete_btn = QPushButton("Delete")
            delete_btn.setStyleSheet("background-color: #F44336; color: white; font-weight: bold;")
            delete_btn.clicked.connect(lambda checked, group_id=group['id']: self.delete_group(group_id))
            self.groups_table.setCellWidget(row, 3, delete_btn)

    def add_page(self):
        """Add a new page."""
        page_name = self.page_name_edit.text().strip()
        page_id = self.page_id_edit.text().strip()
        page_url = self.page_url_edit.text().strip()

        if not page_name or not page_id:
            QMessageBox.warning(self, "Input Required", "Please enter both page name and page ID.")
            return

        if self.parent.add_page_to_account(self.account_id, page_name, page_id, page_url):
            QMessageBox.information(self, "Success", f"Page '{page_name}' added successfully!")
            self.page_name_edit.clear()
            self.page_id_edit.clear()
            self.page_url_edit.clear()
            self.load_pages()
        else:
            QMessageBox.critical(self, "Error", "Failed to add page. Please try again.")

    def add_group(self):
        """Add a new group."""
        group_name = self.group_name_edit.text().strip()
        group_id = self.group_id_edit.text().strip()
        group_url = self.group_url_edit.text().strip()

        if not group_name or not group_id:
            QMessageBox.warning(self, "Input Required", "Please enter both group name and group ID.")
            return

        if self.parent.add_group_to_account(self.account_id, group_name, group_id, group_url):
            QMessageBox.information(self, "Success", f"Group '{group_name}' added successfully!")
            self.group_name_edit.clear()
            self.group_id_edit.clear()
            self.group_url_edit.clear()
            self.load_groups()
        else:
            QMessageBox.critical(self, "Error", "Failed to add group. Please try again.")

    def delete_page(self, page_id):
        """Delete a page."""
        reply = QMessageBox.question(self, "Confirm Delete",
                                   "Are you sure you want to delete this page?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            try:
                with QMutexLocker(db_mutex):
                    c.execute("DELETE FROM pages WHERE id = ?", (page_id,))
                    conn.commit()
                logging.info(f"Deleted page ID {page_id}")
                self.load_pages()
            except Exception as e:
                logging.error(f"Error deleting page: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete page: {e}")

    def delete_group(self, group_id):
        """Delete a group."""
        reply = QMessageBox.question(self, "Confirm Delete",
                                   "Are you sure you want to delete this group?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            try:
                with QMutexLocker(db_mutex):
                    c.execute("DELETE FROM groups WHERE id = ?", (group_id,))
                    conn.commit()
                logging.info(f"Deleted group ID {group_id}")
                self.load_groups()
            except Exception as e:
                logging.error(f"Error deleting group: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete group: {e}")


# --- Main Application Window ---

class FacebookSchedulerMain(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Facebook Auto Poster & Scheduler")
        self.setWindowIcon(QIcon("uploads/logo.png")) # Ensure logo exists
        self.resize(1300, 850) # Slightly larger default size

        # --- Central Data Store (Consider a dedicated DataManager class later) ---
        self.all_posts_data: dict[str, list[PostData]] = {ptype: [] for ptype in POST_CATEGORIES}
        self.all_accounts_data: list[AccountData] = []
        self.all_schedules_data: dict[int, dict] = {} # {tab_index: schedule_info}

        # --- NST Browser Check Control Variables ---
        self.check_all_cancelled = False
        self.nst_check_all_worker = None

        # --- Application Settings ---
        self.settings = load_app_settings()

        # --- UI Elements ---
        self._create_actions()
        self._create_widgets()
        self._create_layouts()
        self._create_connections()
        self._apply_styles()

        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("Ready", 3000)

        # --- Background Workers & Timers ---
        self.post_check_timer = QTimer(self)
        self.post_check_timer.timeout.connect(self.run_post_check_worker)
        self.post_check_worker_instance = None # To hold the running worker

        # Start timer using settings
        check_interval = self.settings.get('check_interval', 60) * 1000  # Convert to milliseconds
        self.post_check_timer.start(check_interval)

        # Run once immediately on start if auto_publish is enabled
        if self.settings.get('auto_publish', True):
            self.run_post_check_worker()

        # --- Initial Data Load ---
        self.load_accounts_data()
        self.load_posts_data()
        self.load_schedules_data()
        self.load_pages_data()
        self.load_groups_data()

        # Load saved NST profiles
        if hasattr(self, 'nstProfilesTable'):
            self.load_saved_nst_profiles()

        # Update auto-posting button text after UI is created
        if hasattr(self, 'autoPostBtn'):
            self.update_auto_post_button_text()

        self.switchPage("Home") # Start on Home page

    def _create_actions(self):
        # Create QAction objects for menus, toolbars if needed later
        pass

    def _create_widgets(self):
        """Create all the main widgets for the UI."""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # Splitter
        self.splitter = QSplitter(Qt.Horizontal)

        # Sidebar (Navigation)
        self.sidebar = QWidget()
        self.sidebar.setMinimumWidth(180)
        self.sidebar.setMaximumWidth(250)

        self.navButtons = {}
        self.navLayout = QVBoxLayout(self.sidebar)
        self.navLayout.setContentsMargins(10, 10, 10, 10)
        self.navLayout.setSpacing(8)

        nav_items = [
            ("Home", "go-home"),
            ("Accounts", "system-users"), # Renamed from Login Facebook
            ("Posts", "document-multiple"), # Renamed from Facebook Posts
            ("Schedules", "view-calendar-month"), # Renamed from Monthly Schedule
        ]
        for name, icon_name in nav_items:
            btn = QPushButton(QIcon.fromTheme(icon_name), f" {name}") # Add space for icon
            btn.setCursor(Qt.PointingHandCursor)
            btn.setObjectName("NavButton") # For styling
            btn.setFixedHeight(35)
            btn.setLayoutDirection(Qt.LeftToRight) # Ensure icon is on the left
            btn.setStyleSheet("text-align: left; padding-left: 10px;") # Align text left
            self.navButtons[name] = btn
            self.navLayout.addWidget(btn)

        self.navLayout.addStretch(1) # Push reset button down

        self.resetBtn = QPushButton(QIcon.fromTheme("edit-reset"), " Reset Software")
        self.resetBtn.setCursor(Qt.PointingHandCursor)
        self.resetBtn.setObjectName("ResetButton") # For styling
        self.resetBtn.setFixedHeight(35)
        self.resetBtn.setStyleSheet("text-align: left; padding-left: 10px;")
        self.navLayout.addWidget(self.resetBtn)

        # Main Content Area (Stacked Widget)
        self.pages = QStackedWidget()
        self.homeWidget = self._create_home_page()
        self.accountsWidget = self._create_accounts_page()
        self.postsWidget = self._create_posts_page()
        self.schedulesWidget = self._create_schedules_page()

        self.pages.addWidget(self.homeWidget)
        self.pages.addWidget(self.accountsWidget)
        self.pages.addWidget(self.postsWidget)
        self.pages.addWidget(self.schedulesWidget)

    def _create_layouts(self):
        """Setup main layout and splitter."""
        mainLayout = QHBoxLayout(self.central_widget)
        mainLayout.setContentsMargins(0, 0, 0, 0) # No margins for main layout

        self.splitter.addWidget(self.sidebar)
        self.splitter.addWidget(self.pages)
        self.splitter.setSizes([200, 1000]) # Initial sizes
        self.splitter.setStretchFactor(1, 1) # Allow content area to expand more

        mainLayout.addWidget(self.splitter)

    def _create_connections(self):
        """Connect signals and slots."""
        # Navigation buttons
        for name, btn in self.navButtons.items():
            # Use partial to pass the name correctly
            btn.clicked.connect(partial(self.switchPage, name))

        # Reset button
        self.resetBtn.clicked.connect(self.resetSoftware)

        # --- Connections within pages (handled in their creation methods) ---


    def _apply_styles(self):
        """Apply QSS styles to the application."""
        # Load from file or define here
        style_sheet = """
            QMainWindow {
                background-color: #F0F4F8; /* Light blue-grey background */
            }
            QWidget#Sidebar { /* Style sidebar specifically */
                background-color: #FFFFFF;
                border-right: 1px solid #D5DDE5;
            }
            QPushButton#NavButton {
                background-color: transparent;
                border: none; /* No border */
                color: #333333;
                padding: 8px 10px;
                border-radius: 4px; /* Slightly rounded corners */
                text-align: left;
                font-size: 11pt;
            }
            QPushButton#NavButton:hover {
                background-color: #E8EFF5; /* Light hover */
                color: #000000;
            }
            QPushButton#NavButton:checked { /* Style for active button */
                background-color: #D2E3F5; /* Blueish background */
                font-weight: bold;
                color: #1A5F9E; /* Darker blue text */
            }

            QPushButton#ResetButton {
                 background-color: transparent;
                 border: none;
                 color: #D9534F; /* Reddish color */
                 padding: 8px 10px;
                 border-radius: 4px;
                 text-align: left;
                 font-size: 11pt;
            }
             QPushButton#ResetButton:hover {
                 background-color: #FBE8E8; /* Light red hover */
             }


            QStackedWidget {
                background-color: #FFFFFF; /* White background for content pages */
            }

            QTabWidget::pane {
                border-top: 1px solid #D5DDE5;
                background: #FFFFFF;
            }
            QTabBar {
                qproperty-expanding: true; /* Allow tabs to expand */
            }
            QTabBar::tab {
                background: #F0F4F8;
                border: 1px solid #D5DDE5;
                border-bottom: none; /* Match pane border */
                padding: 6px 15px; /* Increased horizontal padding */
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                color: #555555;
                min-width: 100px; /* Increased minimum width */
                /* Removed max-width to allow full text display */
            }
            QTabBar::tab:selected {
                background: #FFFFFF; /* Selected tab matches pane */
                color: #1A5F9E;
                font-weight: bold;
            }
             QTabBar::tab:hover {
                 background: #E8EFF5;
             }

            QTableWidget {
                border: 1px solid #D5DDE5;
                gridline-color: #E0E0E0; /* Lighter grid lines */
                alternate-background-color: #F8FAFC; /* Subtle alternating color */
            }
            QHeaderView::section {
                background-color: #E8EFF5; /* Light blue header */
                padding: 4px;
                border: 1px solid #D5DDE5;
                font-weight: bold;
                color: #333;
            }
             QHeaderView::section:horizontal {
                 border-top: none; /* Avoid double border */
             }


            QPushButton {
                background-color: #6495ED; /* Cornflower blue default */
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #4A90E2; /* Darker blue hover */
            }
             QPushButton:pressed {
                 background-color: #3B7ACC;
             }
            QPushButton:disabled {
                 background-color: #B0C4DE; /* Lighter blue disabled */
                 color: #777777;
             }


            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDateEdit, QTimeEdit {
                border: 1px solid #CCCCCC;
                padding: 5px;
                border-radius: 3px;
                background-color: #FFFFFF;
                font-size: 10pt;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus {
                border: 1px solid #6495ED; /* Highlight focus */
            }
            QComboBox::drop-down {
                border: none; /* Clean dropdown arrow */
            }
            QComboBox::down-arrow {
                 /* Use a standard down arrow icon */
                 image: url(:/qt-project.org/styles/commonstyle/images/downarraow-16.png);
                 width: 14px;
                 height: 14px;
             }

            QGroupBox {
                font-weight: bold;
                border: 1px solid #D5DDE5;
                border-radius: 4px;
                margin-top: 10px; /* Space above groupbox */
                padding-top: 15px; /* Space for title */
                background-color: #FDFEFE;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
                left: 10px; /* Position title */
                color: #1A5F9E;
            }

            QStatusBar {
                background-color: #E8EFF5;
                color: #333;
            }
            QStatusBar::item {
                 border: none; /* Remove borders between items */
             }
        """
        self.setStyleSheet(style_sheet)
        self.sidebar.setObjectName("Sidebar") # Assign ID for specific styling

    # --- Page Creation Methods ---

    def _create_home_page(self):
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)

        # Main scroll area for responsive design
        scrollArea = QScrollArea()
        scrollArea.setWidgetResizable(True)
        scrollArea.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scrollArea.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scrollArea.setStyleSheet("QScrollArea { border: none; }")

        scrollContent = QWidget()
        layout = QVBoxLayout(scrollContent)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(24)

        # Modern header section
        headerContainer = QWidget()
        headerContainer.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 16px;
                padding: 24px;
            }
        """)
        headerLayout = QVBoxLayout(headerContainer)
        headerLayout.setContentsMargins(24, 24, 24, 24)

        titleLabel = QLabel("📊 Dashboard")
        titleLabel.setAlignment(Qt.AlignCenter)
        titleLabel.setStyleSheet("""
            font-size: 28pt;
            font-weight: 700;
            color: white;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        """)
        headerLayout.addWidget(titleLabel)

        subtitleLabel = QLabel("Monitor your social media automation")
        subtitleLabel.setAlignment(Qt.AlignCenter)
        subtitleLabel.setStyleSheet("""
            font-size: 14pt;
            color: rgba(255,255,255,0.9);
            font-weight: 400;
        """)
        headerLayout.addWidget(subtitleLabel)

        layout.addWidget(headerContainer)

        # Stats cards container
        statsContainer = QWidget()
        statsLayout = QHBoxLayout(statsContainer)
        statsLayout.setSpacing(16)

        # Create stats cards
        self.create_stats_cards(statsLayout)
        layout.addWidget(statsContainer)

        # Chart section with modern styling
        chartContainer = QWidget()
        chartContainer.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 16px;
                border: 1px solid #e9ecef;
            }
        """)
        chartLayout = QVBoxLayout(chartContainer)
        chartLayout.setContentsMargins(24, 24, 24, 24)

        chartTitle = QLabel("📈 Posts Distribution by Category")
        chartTitle.setStyleSheet("""
            font-size: 18pt;
            font-weight: 600;
            color: #495057;
            margin-bottom: 16px;
        """)
        chartLayout.addWidget(chartTitle)

        # Chart View
        self.postChart = QChart()
        self.postChartView = QChartView(self.postChart)
        self.postChartView.setRenderHint(QPainter.Antialiasing)
        self.postChartView.setMinimumHeight(350)
        self.postChartView.setStyleSheet("border: none; background: transparent;")
        chartLayout.addWidget(self.postChartView)

        layout.addWidget(chartContainer)

        # Activity timeline section
        timelineContainer = QWidget()
        timelineContainer.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 16px;
                border: 1px solid #e9ecef;
            }
        """)
        timelineLayout = QVBoxLayout(timelineContainer)
        timelineLayout.setContentsMargins(24, 24, 24, 24)

        timelineTitle = QLabel("⏰ Recent Activity")
        timelineTitle.setStyleSheet("""
            font-size: 18pt;
            font-weight: 600;
            color: #495057;
            margin-bottom: 16px;
        """)
        timelineLayout.addWidget(timelineTitle)

        self.activityLabel = QLabel("Loading recent activity...")
        self.activityLabel.setStyleSheet("""
            font-size: 12pt;
            color: #6c757d;
            line-height: 1.6;
            padding: 16px;
            background-color: #f8f9fa;
            border-radius: 8px;
        """)
        timelineLayout.addWidget(self.activityLabel)

        layout.addWidget(timelineContainer)

        scrollArea.setWidget(scrollContent)

        # Main layout for the widget
        mainLayout = QVBoxLayout(widget)
        mainLayout.setContentsMargins(0, 0, 0, 0)
        mainLayout.addWidget(scrollArea)

        return widget

    def create_stats_cards(self, layout):
        """Create modern stats cards for the dashboard."""
        # Stats data - will be updated by update_dashboard
        auto_post_status = "🟢 Active" if self.settings.get('auto_publish', True) else "🔴 Stopped"
        auto_post_color = "#28a745" if self.settings.get('auto_publish', True) else "#dc3545"

        stats_data = [
            {"title": "Total Posts", "value": "0", "icon": "📝", "color": "#007bff"},
            {"title": "Active Accounts", "value": "0", "icon": "👤", "color": "#28a745"},
            {"title": "Scheduled Posts", "value": "0", "icon": "📅", "color": "#ffc107"},
            {"title": "Published Today", "value": "0", "icon": "🚀", "color": "#dc3545"},
            {"title": "Auto Posting", "value": auto_post_status, "icon": "⚡", "color": auto_post_color}
        ]

        self.stats_cards = {}

        for stat in stats_data:
            card = self.create_stat_card(stat["title"], stat["value"], stat["icon"], stat["color"])
            self.stats_cards[stat["title"]] = card
            layout.addWidget(card)

    def create_stat_card(self, title, value, icon, color):
        """Create a single stat card widget."""
        card = QWidget()
        card.setFixedHeight(120)
        card.setStyleSheet(f"""
            QWidget {{
                background-color: white;
                border-radius: 12px;
                border: 1px solid #e9ecef;
            }}
            QWidget:hover {{
                border-color: {color};
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(8)

        # Icon and value row
        topLayout = QHBoxLayout()

        iconLabel = QLabel(icon)
        iconLabel.setStyleSheet(f"""
            font-size: 24pt;
            color: {color};
        """)
        topLayout.addWidget(iconLabel)

        topLayout.addStretch()

        valueLabel = QLabel(value)
        valueLabel.setStyleSheet(f"""
            font-size: 24pt;
            font-weight: 700;
            color: {color};
        """)
        valueLabel.setObjectName("valueLabel")  # For easy updates
        topLayout.addWidget(valueLabel)

        layout.addLayout(topLayout)

        # Title
        titleLabel = QLabel(title)
        titleLabel.setStyleSheet("""
            font-size: 12pt;
            font-weight: 500;
            color: #6c757d;
        """)
        layout.addWidget(titleLabel)

        return card

    def _create_accounts_page(self):
        widget = QWidget()
        mainLayout = QVBoxLayout(widget)
        mainLayout.setContentsMargins(15, 15, 15, 15)
        mainLayout.setSpacing(10)

        tabWidget = QTabWidget()
        # Make tabs expand to fill available space and show full text
        tabWidget.tabBar().setExpanding(True)
        tabWidget.tabBar().setUsesScrollButtons(False)
        tabWidget.tabBar().setElideMode(Qt.ElideNone)  # Don't truncate text





        # NST Browser Manager Tab
        nstManagerTab = QWidget()
        nstManagerLayout = QVBoxLayout(nstManagerTab)

        # Header
        headerLayout = QHBoxLayout()
        headerLabel = QLabel("NST Browser Profiles Manager")
        headerLabel.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3;")
        headerLayout.addWidget(headerLabel)
        headerLayout.addStretch()



        nstManagerLayout.addLayout(headerLayout)

        # NST Profiles Table
        self.nstProfilesTable = QTableWidget()
        self.nstProfilesTable.setColumnCount(7)
        self.nstProfilesTable.setHorizontalHeaderLabels([
            "Select", "Profile ID", "Facebook ID", "Status", "Facebook Status", "Actions", "Manage Pages/Groups"
        ])

        # Configure table
        header = self.nstProfilesTable.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)       # Select checkbox
        header.setSectionResizeMode(1, QHeaderView.Interactive)  # Profile ID
        header.setSectionResizeMode(2, QHeaderView.Stretch)     # Facebook ID
        header.setSectionResizeMode(3, QHeaderView.Fixed)       # Status
        header.setSectionResizeMode(4, QHeaderView.Fixed)       # Facebook Status
        header.setSectionResizeMode(5, QHeaderView.Fixed)       # Actions
        header.setSectionResizeMode(6, QHeaderView.Fixed)       # Manage Pages/Groups

        header.resizeSection(0, 60)   # Select checkbox
        header.resizeSection(1, 120)  # Profile ID
        header.resizeSection(3, 80)   # Status
        header.resizeSection(4, 120)  # Facebook Status
        header.resizeSection(5, 150)  # Actions - increased width
        header.resizeSection(6, 150)  # Manage Pages/Groups

        self.nstProfilesTable.setAlternatingRowColors(True)
        self.nstProfilesTable.setSelectionBehavior(QAbstractItemView.SelectRows)

        # Set minimum row height to accommodate buttons
        self.nstProfilesTable.verticalHeader().setDefaultSectionSize(35)
        self.nstProfilesTable.verticalHeader().setMinimumSectionSize(35)

        nstManagerLayout.addWidget(self.nstProfilesTable)

        # Footer buttons
        footerLayout = QHBoxLayout()

        # Selection buttons
        self.nstSelectAllBtn = QPushButton("Select All Profiles")
        self.nstSelectAllBtn.clicked.connect(lambda: self._select_all_table_rows(self.nstProfilesTable, True))
        footerLayout.addWidget(self.nstSelectAllBtn)

        self.nstDeselectAllBtn = QPushButton("Deselect Profiles")
        self.nstDeselectAllBtn.clicked.connect(lambda: self._select_all_table_rows(self.nstProfilesTable, False))
        footerLayout.addWidget(self.nstDeselectAllBtn)

        self.nstDeleteSelectedBtn = QPushButton("Delete Selected Profiles")
        self.nstDeleteSelectedBtn.clicked.connect(self.delete_selected_nst_profiles)
        self.nstDeleteSelectedBtn.setStyleSheet("background-color: #F44336; color: white; font-weight: bold; padding: 8px;")
        footerLayout.addWidget(self.nstDeleteSelectedBtn)

        footerLayout.addStretch()

        # Add Profile List button (popup)
        self.addProfileListBtn = QPushButton("Add Profile List")
        self.addProfileListBtn.clicked.connect(self.add_profiles_to_list_popup)
        self.addProfileListBtn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        footerLayout.addWidget(self.addProfileListBtn)

        self.checkAllNSTBtn = QPushButton("Check All Status")
        self.checkAllNSTBtn.clicked.connect(self.check_all_nst_status)
        footerLayout.addWidget(self.checkAllNSTBtn)

        self.cancelCheckAllBtn = QPushButton("Cancel Check All")
        self.cancelCheckAllBtn.clicked.connect(self.cancel_check_all_nst_status)
        self.cancelCheckAllBtn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 8px;")
        self.cancelCheckAllBtn.setVisible(False)  # Hidden by default
        footerLayout.addWidget(self.cancelCheckAllBtn)

        nstManagerLayout.addLayout(footerLayout)

        # Add Tabs to Widget
        tabWidget.addTab(nstManagerTab, QIcon.fromTheme("computer"), "NST Browser Manager")


        mainLayout.addWidget(tabWidget)
        return widget

    def _create_posts_page(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Header (Optional)
        # headerLabel = QLabel("Manage Posts") ... layout.addWidget(headerLabel)

        self.postsTabs = QTabWidget()
        # Make tabs expand to fill available space and show full text
        self.postsTabs.tabBar().setExpanding(True)
        self.postsTabs.tabBar().setUsesScrollButtons(False)
        self.postsTabs.tabBar().setElideMode(Qt.ElideNone)  # Don't truncate text
        self.postsTables: dict[str, PostsTableWidget] = {} # Store table widgets

        for post_type in POST_CATEGORIES:
            table = PostsTableWidget(post_type=post_type)
            # Connect signals if needed, e.g., table.editRequest.connect(...)
            self.postsTables[post_type] = table
            # Add icon based on type?
            icon = QIcon()
            if post_type == "Recipes": icon = QIcon.fromTheme("applications-accessories")
            elif post_type == "Engage": icon = QIcon.fromTheme("mail-mark-unread")
            elif post_type == "Parole": icon = QIcon.fromTheme("security-high")
            self.postsTabs.addTab(table, icon, post_type)

        layout.addWidget(self.postsTabs)

        # Footer Buttons
        footerLayout = QHBoxLayout()
        self.createPostBtn = QPushButton(QIcon.fromTheme("list-add"), "Create New Post")
        self.createPostBtn.clicked.connect(self.open_create_post_dialog)
        footerLayout.addWidget(self.createPostBtn)

        # Select All / Deselect All buttons
        self.selectAllPostsBtn = QPushButton("Select All")
        self.selectAllPostsBtn.clicked.connect(self.select_all_current_posts)
        footerLayout.addWidget(self.selectAllPostsBtn)

        self.deselectAllPostsBtn = QPushButton("Deselect All")
        self.deselectAllPostsBtn.clicked.connect(self.deselect_all_current_posts)
        footerLayout.addWidget(self.deselectAllPostsBtn)

        footerLayout.addStretch()

        self.editSelectedPostBtn = QPushButton(QIcon.fromTheme("document-edit"), "Edit Selected")
        self.editSelectedPostBtn.clicked.connect(self.edit_selected_post)
        footerLayout.addWidget(self.editSelectedPostBtn)

        self.removeSelectedPostsBtn = QPushButton(QIcon.fromTheme("edit-delete"), "Remove Selected")
        self.removeSelectedPostsBtn.setStyleSheet("background-color: #D9534F;") # Red remove button
        self.removeSelectedPostsBtn.clicked.connect(self.remove_selected_posts)
        footerLayout.addWidget(self.removeSelectedPostsBtn)

        layout.addLayout(footerLayout)
        return widget

    def _create_schedules_page(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Header (Optional)
        # mLabel = QLabel("Monthly Schedules") ... layout.addWidget(mLabel)

        self.schedulesTabWidget = QTabWidget()
        # Make tabs expand to fill available space and show full text
        self.schedulesTabWidget.tabBar().setExpanding(True)
        self.schedulesTabWidget.tabBar().setUsesScrollButtons(False)
        self.schedulesTabWidget.tabBar().setElideMode(Qt.ElideNone)  # Don't truncate text
        self.schedulesTabWidget.setTabsClosable(True) # Allow closing tabs
        self.schedulesTabWidget.tabCloseRequested.connect(self.remove_schedule_tab) # Handle close request
        layout.addWidget(self.schedulesTabWidget)

        # Footer Buttons
        mBtnLayout = QHBoxLayout()
        self.createScheduleBtn = QPushButton(QIcon.fromTheme("calendar-new"), "Create New Schedule")
        self.createScheduleBtn.clicked.connect(self.open_create_schedule_dialog)
        mBtnLayout.addWidget(self.createScheduleBtn)

        self.refreshSchedulesBtn = QPushButton(QIcon.fromTheme("view-refresh"), "Refresh Schedules")
        self.refreshSchedulesBtn.setToolTip("Reload post content/images in all schedules from the database")
        self.refreshSchedulesBtn.clicked.connect(self.refresh_all_schedules)
        mBtnLayout.addWidget(self.refreshSchedulesBtn)

        # Auto-posting control button
        self.autoPostBtn = QPushButton()
        self.autoPostBtn.setFixedHeight(35)
        self.autoPostBtn.clicked.connect(self.toggle_auto_posting)
        self.update_auto_post_button_text()
        mBtnLayout.addWidget(self.autoPostBtn)

        mBtnLayout.addStretch()

        # Buttons for the *current* schedule tab (might need enabling/disabling)
        self.selectAllScheduleBtn = QPushButton("Select All Rows")
        self.selectAllScheduleBtn.clicked.connect(self.select_all_schedule_rows)
        mBtnLayout.addWidget(self.selectAllScheduleBtn)

        self.deselectAllScheduleBtn = QPushButton("Deselect All Rows")
        self.deselectAllScheduleBtn.clicked.connect(self.deselect_all_schedule_rows)
        mBtnLayout.addWidget(self.deselectAllScheduleBtn)

        self.removeSelectedScheduleRowsBtn = QPushButton(QIcon.fromTheme("edit-delete"), "Remove Selected Rows")
        self.removeSelectedScheduleRowsBtn.setStyleSheet("background-color: #D9534F;")
        self.removeSelectedScheduleRowsBtn.clicked.connect(self.remove_selected_schedule_rows)
        mBtnLayout.addWidget(self.removeSelectedScheduleRowsBtn)

        layout.addLayout(mBtnLayout)

        # Initially disable buttons that depend on a selected tab
        self.selectAllScheduleBtn.setEnabled(False)
        self.deselectAllScheduleBtn.setEnabled(False)
        self.removeSelectedScheduleRowsBtn.setEnabled(False)
        self.schedulesTabWidget.currentChanged.connect(self._update_schedule_button_states)


        return widget

    def _create_pages_manager_page(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        header = QLabel("Manage Facebook Pages")
        header.setStyleSheet("font-size: 14pt; font-weight: bold; color: #333; margin-bottom: 10px;")
        layout.addWidget(header)

        self.pagesTable = QTableWidget() # Standard table
        self.pagesTable.setColumnCount(4)
        self.pagesTable.setHorizontalHeaderLabels(["", "Name", "Page ID", "Link"])
        # Setup header resizing
        hHeader = self.pagesTable.horizontalHeader()
        hHeader.setSectionResizeMode(1, QHeaderView.Stretch) # Name stretch
        hHeader.setSectionResizeMode(0, QHeaderView.Fixed) # Checkbox fixed
        hHeader.setSectionResizeMode(2, QHeaderView.Interactive) # ID interactive
        hHeader.setSectionResizeMode(3, QHeaderView.Interactive) # Link interactive
        hHeader.resizeSection(0, 30)
        hHeader.resizeSection(2, 150)
        hHeader.resizeSection(3, 200)
        self.pagesTable.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.pagesTable.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.pagesTable.verticalHeader().setVisible(False)
        self.pagesTable.setAlternatingRowColors(True)
        self.pagesTable.itemDoubleClicked.connect(self.open_page_or_group_link) # Double click to open link
        layout.addWidget(self.pagesTable)

        # Footer Buttons
        footerLayout = QHBoxLayout()
        self.pagesSelectAllBtn = QPushButton("Select All")
        self.pagesSelectAllBtn.clicked.connect(lambda: self._select_all_table_rows(self.pagesTable, True))
        footerLayout.addWidget(self.pagesSelectAllBtn)

        self.pagesDeselectAllBtn = QPushButton("Deselect All")
        self.pagesDeselectAllBtn.clicked.connect(lambda: self._select_all_table_rows(self.pagesTable, False))
        footerLayout.addWidget(self.pagesDeselectAllBtn)

        # Add Delete Selected button
        self.pagesDeleteSelectedBtn = QPushButton(QIcon.fromTheme("edit-delete"), "Delete Selected")
        self.pagesDeleteSelectedBtn.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 6px 12px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #d32f2f; }")
        self.pagesDeleteSelectedBtn.clicked.connect(lambda: self._delete_selected_pages_or_groups(is_pages=True))
        footerLayout.addWidget(self.pagesDeleteSelectedBtn)

        footerLayout.addStretch()

        layout.addLayout(footerLayout)
        return widget

    def _create_groups_manager_page(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        header = QLabel("Manage Facebook Groups")
        header.setStyleSheet("font-size: 14pt; font-weight: bold; color: #333; margin-bottom: 10px;")
        layout.addWidget(header)

        self.groupsTable = QTableWidget() # Standard table
        self.groupsTable.setColumnCount(4)
        self.groupsTable.setHorizontalHeaderLabels(["", "Name", "Group ID", "Link"])
         # Setup header resizing (same as pages)
        hHeader = self.groupsTable.horizontalHeader()
        hHeader.setSectionResizeMode(1, QHeaderView.Stretch)
        hHeader.setSectionResizeMode(0, QHeaderView.Fixed)
        hHeader.setSectionResizeMode(2, QHeaderView.Interactive)
        hHeader.setSectionResizeMode(3, QHeaderView.Interactive)
        hHeader.resizeSection(0, 30)
        hHeader.resizeSection(2, 150)
        hHeader.resizeSection(3, 200)
        self.groupsTable.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.groupsTable.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.groupsTable.verticalHeader().setVisible(False)
        self.groupsTable.setAlternatingRowColors(True)
        self.groupsTable.itemDoubleClicked.connect(self.open_page_or_group_link) # Double click to open link
        layout.addWidget(self.groupsTable)

        # Footer Buttons
        footerLayout = QHBoxLayout()
        self.groupsSelectAllBtn = QPushButton("Select All")
        self.groupsSelectAllBtn.clicked.connect(lambda: self._select_all_table_rows(self.groupsTable, True))
        footerLayout.addWidget(self.groupsSelectAllBtn)

        self.groupsDeselectAllBtn = QPushButton("Deselect All")
        self.groupsDeselectAllBtn.clicked.connect(lambda: self._select_all_table_rows(self.groupsTable, False))
        footerLayout.addWidget(self.groupsDeselectAllBtn)

        # Add Delete Selected button
        self.groupsDeleteSelectedBtn = QPushButton(QIcon.fromTheme("edit-delete"), "Delete Selected")
        self.groupsDeleteSelectedBtn.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 6px 12px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #d32f2f; }")
        self.groupsDeleteSelectedBtn.clicked.connect(lambda: self._delete_selected_pages_or_groups(is_pages=False))
        footerLayout.addWidget(self.groupsDeleteSelectedBtn)

        footerLayout.addStretch()

        layout.addLayout(footerLayout)
        return widget

    # --- Data Loading Methods ---

    def load_accounts_data(self):
        """Loads account data from DB and updates UI."""
        logging.debug("Loading accounts data...")
        try:
            with QMutexLocker(db_mutex):
                rows = c.execute("SELECT id, fb_id, full_name, cookie, status, nst_profile_id, browser_type FROM accounts ORDER BY full_name").fetchall()
            self.all_accounts_data = [AccountData(**row) for row in rows]
            logging.info(f"Loaded {len(self.all_accounts_data)} accounts.")
            # Update UI (Manage Accounts Table) - removed since Manager Accounts tab was deleted
        except sqlite3.Error as e:
            logging.error(f"Failed to load accounts: {e}")
            QMessageBox.critical(self, "Database Error", f"Could not load accounts: {e}")
        self.update_dashboard() # Update dashboard stats

    def load_posts_data(self):
        """Loads post data from DB and updates UI."""
        logging.debug("Loading posts data...")
        try:
            with QMutexLocker(db_mutex):
                rows = c.execute("""
                    SELECT id, content, website_link, image_path, scheduled_time,
                           account_id, status, created_at, post_type, recipe_text
                    FROM posts ORDER BY created_at DESC
                """).fetchall()

            # Clear existing data
            for ptype in self.all_posts_data:
                 self.all_posts_data[ptype] = []

            # Process rows into PostData objects
            for row_dict in rows:
                 post = PostData(**row_dict)
                 # Convert timestamp if stored differently (e.g., TEXT)
                 # if isinstance(post.scheduled_time, str): post.scheduled_time = ...
                 # if isinstance(post.created_at, str): post.created_at = ...
                 if post.post_type in self.all_posts_data:
                     self.all_posts_data[post.post_type].append(post)

            logging.info(f"Loaded {len(rows)} total posts.")

            # Update UI (Post Tables)
            if hasattr(self, 'postsTables'):
                for post_type, table_widget in self.postsTables.items():
                    table_widget.populate_table(self.all_posts_data.get(post_type, []))

                # Update tab titles with post counts
                self.update_posts_tab_titles()

        except sqlite3.Error as e:
            logging.error(f"Failed to load posts: {e}")
            QMessageBox.critical(self, "Database Error", f"Could not load posts: {e}")
        self.update_dashboard() # Update dashboard stats

    def load_schedules_data(self):
        """Loads saved schedules from DB and populates tabs."""
        logging.debug("Loading schedules data...")
        try:
            with QMutexLocker(db_mutex):
                rows = c.execute("SELECT id, schedule_json, created_at FROM schedules ORDER BY created_at").fetchall()

            # Clear existing tabs and internal data first
            self.schedulesTabWidget.clear()
            self.all_schedules_data.clear()

            if not rows:
                self._add_placeholder_schedule_tab()
                return

            # Add tabs for each schedule
            for row in rows:
                schedule_id = row['id']
                try:
                    schedule_list = json.loads(row['schedule_json'])
                    if isinstance(schedule_list, list):
                         # Convert account dictionaries back to AccountData objects
                         for entry in schedule_list:
                             if 'account' in entry and isinstance(entry['account'], dict):
                                 account_dict = entry['account']
                                 entry['account'] = AccountData(
                                     id=account_dict.get('id', -1),
                                     fb_id=account_dict.get('fb_id', ''),
                                     full_name=account_dict.get('full_name'),
                                     cookie=account_dict.get('cookie'),
                                     status=account_dict.get('status', 'live'),
                                     nst_profile_id=account_dict.get('nst_profile_id'),
                                     browser_type=account_dict.get('browser_type', 'chrome')
                                 )

                         self._add_schedule_tab(schedule_list, schedule_id, tab_name=f"Schedule {schedule_id}")
                    else:
                         logging.warning(f"Schedule ID {schedule_id} JSON is not a list, skipping.")
                except json.JSONDecodeError:
                    logging.error(f"Failed to decode JSON for schedule ID {schedule_id}, skipping.")

            logging.info(f"Loaded {len(rows)} schedules.")
            if self.schedulesTabWidget.count() == 0: # Add placeholder if loading failed
                 self._add_placeholder_schedule_tab()

        except sqlite3.Error as e:
            logging.error(f"Failed to load schedules: {e}")
            QMessageBox.critical(self, "Database Error", f"Could not load schedules: {e}")
            self._add_placeholder_schedule_tab() # Add placeholder on error

        self._update_schedule_button_states() # Update buttons based on current tab

    def load_pages_data(self):
        """Load pages data from database."""
        try:
            with QMutexLocker(db_mutex):
                c.execute("SELECT id, name, page_id, url, source, account_id FROM pages ORDER BY name")
                rows = c.fetchall()

                self.all_pages_data = []
                for row in rows:
                    page = {
                        'id': row[0],
                        'name': row[1],
                        'page_id': row[2],
                        'url': row[3],
                        'source': row[4],
                        'account_id': row[5]
                    }
                    self.all_pages_data.append(page)

                logging.info(f"Loaded {len(self.all_pages_data)} pages from database")

                # Update UI if exists
                if hasattr(self, 'pagesTable'):
                    self.populate_pages_table()

        except Exception as e:
            logging.error(f"Error loading pages data: {e}")
            self.all_pages_data = []

    def load_groups_data(self):
        """Load groups data from database."""
        try:
            with QMutexLocker(db_mutex):
                c.execute("SELECT id, name, group_id, url, source, account_id FROM groups ORDER BY name")
                rows = c.fetchall()

                self.all_groups_data = []
                for row in rows:
                    group = {
                        'id': row[0],
                        'name': row[1],
                        'group_id': row[2],
                        'url': row[3],
                        'source': row[4],
                        'account_id': row[5]
                    }
                    self.all_groups_data.append(group)

                logging.info(f"Loaded {len(self.all_groups_data)} groups from database")

                # Update UI if exists
                if hasattr(self, 'groupsTable'):
                    self.populate_groups_table()

        except Exception as e:
            logging.error(f"Error loading groups data: {e}")
            self.all_groups_data = []

    def get_pages_for_account(self, account_id):
        """Get pages associated with a specific account."""
        try:
            with QMutexLocker(db_mutex):
                c.execute("SELECT id, name, page_id, url FROM pages WHERE account_id = ? ORDER BY name", (account_id,))
                rows = c.fetchall()

                pages = []
                for row in rows:
                    page = {
                        'id': row[0],
                        'name': row[1],
                        'page_id': row[2],
                        'url': row[3]
                    }
                    pages.append(page)

                return pages
        except Exception as e:
            logging.error(f"Error getting pages for account {account_id}: {e}")
            return []

    def get_groups_for_account(self, account_id):
        """Get groups associated with a specific account."""
        try:
            with QMutexLocker(db_mutex):
                c.execute("SELECT id, name, group_id, url FROM groups WHERE account_id = ? ORDER BY name", (account_id,))
                rows = c.fetchall()

                groups = []
                for row in rows:
                    group = {
                        'id': row[0],
                        'name': row[1],
                        'group_id': row[2],
                        'url': row[3]
                    }
                    groups.append(group)

                return groups
        except Exception as e:
            logging.error(f"Error getting groups for account {account_id}: {e}")
            return []

    def add_page_to_account(self, account_id, page_name, page_id, url=""):
        """Add a page to a specific account."""
        try:
            with QMutexLocker(db_mutex):
                c.execute("""
                    INSERT INTO pages (name, page_id, url, account_id, source)
                    VALUES (?, ?, ?, ?, 'manual')
                """, (page_name, page_id, url, account_id))
                conn.commit()
                logging.info(f"Added page {page_name} ({page_id}) to account {account_id}")
                return True
        except Exception as e:
            logging.error(f"Error adding page to account: {e}")
            return False

    def add_group_to_account(self, account_id, group_name, group_id, url=""):
        """Add a group to a specific account."""
        try:
            with QMutexLocker(db_mutex):
                c.execute("""
                    INSERT INTO groups (name, group_id, url, account_id, source)
                    VALUES (?, ?, ?, ?, 'manual')
                """, (group_name, group_id, url, account_id))
                conn.commit()
                logging.info(f"Added group {group_name} ({group_id}) to account {account_id}")
                return True
        except Exception as e:
            logging.error(f"Error adding group to account: {e}")
            return False

    def manage_profile_pages_groups(self, profile_id):
        """Open dialog to manage pages and groups for a specific NST Browser profile."""
        # First, get the account ID for this profile
        account_id = None
        try:
            with QMutexLocker(db_mutex):
                c.execute("SELECT id FROM accounts WHERE nst_profile_id = ?", (profile_id,))
                result = c.fetchone()
                if result:
                    account_id = result[0]
                else:
                    # Create account entry if it doesn't exist
                    fb_id = f"nst_{profile_id}_{random.randint(1000, 9999)}"
                    full_name = f"NST Profile {profile_id}"
                    c.execute("""
                        INSERT INTO accounts (fb_id, full_name, nst_profile_id, browser_type, status)
                        VALUES (?, ?, ?, 'nst', 'live')
                    """, (fb_id, full_name, profile_id))
                    conn.commit()
                    account_id = c.lastrowid
                    logging.info(f"Created account entry for NST profile {profile_id}")
        except Exception as e:
            logging.error(f"Error getting/creating account for profile {profile_id}: {e}")
            QMessageBox.critical(self, "Database Error", f"Error accessing database: {e}")
            return

        if not account_id:
            QMessageBox.warning(self, "Error", "Could not find or create account for this profile.")
            return

        # Open the management dialog
        dialog = ManagePagesGroupsDialog(self, profile_id, account_id)
        dialog.exec_()

    def populate_pages_table(self):
        """Populate the pages table with loaded data."""
        if hasattr(self, 'pagesTable') and hasattr(self, 'all_pages_data'):
            # Convert data format to match _update_page_group_table expectations
            formatted_data = []
            for page in self.all_pages_data:
                formatted_page = {
                    'name': page.get('name', 'N/A'),
                    'id': page.get('page_id', 'N/A'),
                    'link': page.get('url', '')
                }
                formatted_data.append(formatted_page)
            self._update_page_group_table(self.pagesTable, formatted_data)

    def populate_groups_table(self):
        """Populate the groups table with loaded data."""
        if hasattr(self, 'groupsTable') and hasattr(self, 'all_groups_data'):
            # Convert data format to match _update_page_group_table expectations
            formatted_data = []
            for group in self.all_groups_data:
                formatted_group = {
                    'name': group.get('name', 'N/A'),
                    'id': group.get('group_id', 'N/A'),
                    'link': group.get('url', '')
                }
                formatted_data.append(formatted_group)
            self._update_page_group_table(self.groupsTable, formatted_data)

    def _add_placeholder_schedule_tab(self):
        """Adds a tab indicating no schedules are present."""
        placeholder_widget = QWidget()
        layout = QVBoxLayout(placeholder_widget)
        label = QLabel("No schedules created yet.\n\nClick 'Create New Schedule' below to get started.")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("font-size: 12pt; color: grey;")
        layout.addWidget(label)
        self.schedulesTabWidget.addTab(placeholder_widget, QIcon.fromTheme("calendar-view-day"), "No Schedules")
        self.schedulesTabWidget.setTabsClosable(False) # Can't close placeholder


    # --- UI Update and Action Methods ---

    def switchPage(self, sectionName):
        """Switches the main content area to the selected page."""
        logging.debug(f"Switching page to: {sectionName}")
        mapping = {
            "Home": 0, "Accounts": 1, "Posts": 2,
            "Schedules": 3
        }
        index = mapping.get(sectionName, 0)
        self.pages.setCurrentIndex(index)
        self._updateNavButtonStyles(sectionName)

        # Trigger data refresh/UI update for the selected page if needed
        if sectionName == "Home":
            self.update_dashboard()
        elif sectionName == "Accounts":
            # Account data is loaded initially, maybe refresh status?
            pass
        elif sectionName == "Posts":
            # Post data loaded initially
            pass
        elif sectionName == "Schedules":
             self._update_schedule_button_states()


    def _updateNavButtonStyles(self, selectedName):
        """Updates the visual state of navigation buttons."""
        for name, btn in self.navButtons.items():
            btn.setChecked(name == selectedName) # Use setChecked for QSS :checked state


    def update_dashboard(self):
        """Updates the home page dashboard chart and stats."""
        if not hasattr(self, 'postChart') or not self.pages.currentWidget() == self.homeWidget:
            return # Don't update if chart doesn't exist or Home isn't visible

        logging.debug("Updating dashboard...")

        # --- Update Stats Cards ---
        self.update_stats_cards()

        # --- Update Pie Chart ---
        self.postChart.removeAllSeries() # Clear previous data
        series = QPieSeries()
        series.setHoleSize(0.35) # Make it a donut chart

        post_counts = {}
        total_posts = 0
        try:
             with QMutexLocker(db_mutex):
                  rows = c.execute("SELECT post_type, COUNT(*) as count FROM posts GROUP BY post_type").fetchall()
             for row in rows:
                  ptype = row['post_type'] if row['post_type'] else "Uncategorized"
                  count = row['count']
                  post_counts[ptype] = count
                  total_posts += count
        except sqlite3.Error as e:
             logging.error(f"Dashboard DB error (post counts): {e}")
             if hasattr(self, 'activityLabel'):
                 self.activityLabel.setText("Error loading post counts.")
             return # Stop update if DB fails

        if total_posts == 0:
            slice_ = series.append("No Posts Yet", 1)
            slice_.setColor(QColor("#E0E0E0")) # Grey color for no posts
            slice_.setLabelVisible(True)
        else:
            # Sort by count descending for better visual hierarchy
            sorted_counts = sorted(post_counts.items(), key=lambda item: item[1], reverse=True)
            for ptype, count in sorted_counts:
                # Add slice with count and percentage
                percentage = (count / total_posts) * 100 if total_posts > 0 else 0
                slice_ = series.append(f"{ptype} ({count})", count)
                slice_.setLabel(f"{ptype}\n{percentage:.1f}%") # Label with percentage
                slice_.setLabelVisible(True)
                # Add hover effect (optional)
                # slice_.hovered.connect(lambda hovered, s=slice_: self._handle_slice_hover(hovered, s))

        self.postChart.addSeries(series)
        self.postChart.setTitle("")  # Remove title as we have it in the container
        self.postChart.legend().setVisible(True)
        self.postChart.legend().setAlignment(Qt.AlignBottom)
        self.postChart.setAnimationOptions(QChart.SeriesAnimations) # Add animation

        # Modern chart styling
        self.postChart.setBackgroundBrush(QBrush(QColor("transparent")))
        self.postChart.setPlotAreaBackgroundBrush(QBrush(QColor("transparent")))

        # --- Update Activity Label ---
        self.update_activity_timeline()

    def update_stats_cards(self):
        """Update the values in stats cards."""
        if not hasattr(self, 'stats_cards'):
            return

        try:
            with QMutexLocker(db_mutex):
                # Total posts
                total_posts = c.execute("SELECT COUNT(*) FROM posts").fetchone()[0]

                # Active accounts
                active_accounts = c.execute("SELECT COUNT(*) FROM accounts WHERE status = 'live'").fetchone()[0]

                # Scheduled posts (pending status)
                scheduled_posts = c.execute("SELECT COUNT(*) FROM posts WHERE status = 'pending'").fetchone()[0]

                # Published today
                today = datetime.date.today().strftime('%Y-%m-%d')
                published_today = c.execute(
                    "SELECT COUNT(*) FROM posts WHERE status = 'posted' AND DATE(scheduled_time) = ?",
                    (today,)
                ).fetchone()[0]

            # Auto posting status
            auto_post_status = "🟢 Active" if self.settings.get('auto_publish', True) else "🔴 Stopped"

            # Update card values
            stats_values = {
                "Total Posts": str(total_posts),
                "Active Accounts": str(active_accounts),
                "Scheduled Posts": str(scheduled_posts),
                "Published Today": str(published_today),
                "Auto Posting": auto_post_status
            }

            for title, value in stats_values.items():
                if title in self.stats_cards:
                    card = self.stats_cards[title]
                    value_label = card.findChild(QLabel, "valueLabel")
                    if value_label:
                        value_label.setText(value)

                    # Update Auto Posting card color based on status
                    if title == "Auto Posting":
                        color = "#28a745" if self.settings.get('auto_publish', True) else "#dc3545"
                        card.setStyleSheet(f"""
                            QWidget {{
                                background-color: white;
                                border-radius: 12px;
                                border: 1px solid #e9ecef;
                            }}
                            QWidget:hover {{
                                border-color: {color};
                            }}
                        """)

        except sqlite3.Error as e:
            logging.error(f"Error updating stats cards: {e}")

    def update_activity_timeline(self):
        """Update the activity timeline section."""
        if not hasattr(self, 'activityLabel'):
            return

        try:
            with QMutexLocker(db_mutex):
                # Get recent posts
                recent_posts = c.execute("""
                    SELECT post_type, status, scheduled_time, created_at
                    FROM posts
                    ORDER BY created_at DESC
                    LIMIT 5
                """).fetchall()

                if not recent_posts:
                    self.activityLabel.setText("No recent activity found.")
                    return

                activity_text = ""
                for post in recent_posts:
                    post_type = post[0] or "Unknown"
                    status = post[1] or "pending"
                    created_at = post[3]

                    # Format the timestamp
                    try:
                        created_time = datetime.datetime.fromisoformat(created_at)
                        time_str = created_time.strftime("%m/%d %H:%M")
                    except:
                        time_str = "Unknown time"

                    status_emoji = {
                        'posted': '✅',
                        'pending': '⏳',
                        'failed': '❌'
                    }.get(status, '⏳')

                    activity_text += f"{status_emoji} {post_type} post - {status.title()} ({time_str})\n"

                self.activityLabel.setText(activity_text.strip())

        except sqlite3.Error as e:
            logging.error(f"Error updating activity timeline: {e}")
            self.activityLabel.setText("Error loading recent activity.")

        logging.debug("Dashboard updated.")


    # --- Account Management Actions ---
    # Traditional Facebook login removed - now using NST Browser profiles only

    # Traditional Facebook login functions removed - now using NST Browser profiles only




    # --- NST Browser Integration Methods ---

# update_account_combo removed - no longer needed since we don't link to existing accounts









    def _add_nst_profile_task(self, profile_id):
        """Add NST profile task - extract Facebook info."""
        try:
            # Kill any existing processes
            kill_nstchrome_processes()
            time.sleep(2)

            # Launch browser
            debugger_address = launch_and_connect_to_browser(profile_id)
            if not debugger_address:
                raise Exception("Failed to launch NST Browser")

            # Connect with Selenium
            test_driver = exec_selenium_nst(debugger_address)
            if not test_driver:
                raise Exception("Failed to connect Selenium to NST Browser")

            # Navigate to Facebook /me to get ID
            logging.info(f"Getting Facebook ID for NST profile {profile_id}")
            test_driver.get("https://www.facebook.com/me")
            time.sleep(5)

            # Check if logged in
            if "login" in test_driver.current_url.lower():
                raise Exception("Not logged in to Facebook. Please log in to Facebook in this NST Browser profile first.")

            # Extract Facebook ID from /me redirect
            fb_id = None
            current_url = test_driver.current_url

            if "profile.php?id=" in current_url:
                fb_id = current_url.split("profile.php?id=")[1].split("&")[0]
                logging.info(f"Extracted Facebook ID from URL: {fb_id}")
            else:
                # Try alternative methods for custom usernames
                real_uid = _extract_fb_uid_selenium(test_driver)
                fb_id = real_uid if real_uid else None

            if not fb_id:
                raise Exception("Could not extract Facebook ID. Make sure you're logged in to Facebook.")

            # Extract name
            real_name = _extract_full_name_selenium(test_driver)
            full_name = real_name if real_name else f"NST Profile {profile_id}"

            logging.info(f"Successfully extracted info for NST profile {profile_id}: ID={fb_id}, Name={full_name}")

            # Close browser
            test_driver.quit()

            return {
                "profile_id": profile_id,
                "fb_id": fb_id,
                "full_name": full_name
            }

        except Exception as e:
            logging.error(f"NST profile add failed: {e}")
            raise e

    def on_nst_add_finished(self, result, error):
        """Handle NST profile add result."""
        self.addNSTBtn.setEnabled(True)
        self.addNSTBtn.setText("Add NST Profile")

        if error:
            QMessageBox.critical(self, "Add Profile Failed",
                               f"Failed to add NST Profile.\nError: {error}\n\n"
                               "Please ensure:\n"
                               "1. NST Browser is running\n"
                               "2. Profile ID is correct\n"
                               "3. Facebook is logged in for this profile")
            self.statusBar.showMessage(f"Add profile failed: {error}", 5000)
        elif result:
            # Save to database
            try:
                with QMutexLocker(db_mutex):
                    # First check if this NST profile is already added
                    c.execute("SELECT id, fb_id, full_name FROM accounts WHERE nst_profile_id = ?", (result['profile_id'],))
                    existing_nst = c.fetchone()

                    # Check if this Facebook ID already exists
                    c.execute("SELECT id, nst_profile_id, full_name FROM accounts WHERE fb_id = ?", (result['fb_id'],))
                    existing_fb = c.fetchone()

                    if existing_nst:
                        QMessageBox.warning(self, "Profile Already Exists",
                                          f"NST Profile {result['profile_id']} is already added!\n\n"
                                          f"Existing account: {existing_nst[2]} ({existing_nst[1]})")
                        self.statusBar.showMessage("NST Profile already exists.", 3000)
                        return

                    if existing_fb:
                        # Facebook account exists but with different NST profile
                        reply = QMessageBox.question(self, "Facebook Account Exists",
                                                   f"Facebook account {result['fb_id']} already exists!\n\n"
                                                   f"Current: {existing_fb[2]} (NST: {existing_fb[1] or 'None'})\n"
                                                   f"New: {result['full_name']} (NST: {result['profile_id']})\n\n"
                                                   f"Do you want to update it with the new NST profile?",
                                                   QMessageBox.Yes | QMessageBox.No)
                        if reply != QMessageBox.Yes:
                            self.statusBar.showMessage("Operation cancelled.", 3000)
                            return

                        # Update existing account
                        c.execute("""
                            UPDATE accounts SET
                                full_name=?, nst_profile_id=?, browser_type='nst', status='live'
                            WHERE fb_id=?
                        """, (result['full_name'], result['profile_id'], result['fb_id']))
                        action = "updated"
                    else:
                        # Insert new account
                        c.execute("""
                            INSERT INTO accounts (fb_id, full_name, nst_profile_id, browser_type, status)
                            VALUES (?, ?, ?, 'nst', 'live')
                        """, (result['fb_id'], result['full_name'], result['profile_id']))
                        action = "added"

                    conn.commit()

                logging.info(f"NST Profile {result['profile_id']} {action} successfully: {result['full_name']} ({result['fb_id']})")
                QMessageBox.information(self, f"Profile {action.title()} Successfully",
                                      f"NST Profile {action} successfully!\n\n"
                                      f"Profile ID: {result['profile_id']}\n"
                                      f"Facebook Name: {result['full_name']}\n"
                                      f"Facebook ID: {result['fb_id']}")
                self.statusBar.showMessage(f"NST Profile {result['profile_id']} {action}.", 4000)

                # Clear form and refresh data
                self.nstProfileEdit.clear()
                self.load_accounts_data()

            except sqlite3.Error as e:
                logging.error(f"Database error saving NST profile: {e}")
                QMessageBox.critical(self, "Database Error", f"Could not save NST profile: {e}")
        else:
            QMessageBox.warning(self, "Add Profile Issue", "Profile add completed but with unexpected result.")
            self.statusBar.showMessage("NST profile add completed with unexpected result.", 5000)

    # NST Browser Manager Functions






    def check_nst_profile_status(self, profile_id):
        """Check status of a specific NST profile."""
        self.statusBar.showMessage(f"Checking status of profile {profile_id}...")

        # Run in background thread
        self.nst_check_worker = BaseWorker(self._check_nst_profile_task, profile_id)
        self.nst_check_worker.finished.connect(lambda result, error: self.on_nst_check_finished(result, error, profile_id))
        self.nst_check_worker.start()

    def _check_nst_profile_task(self, profile_id):
        """Background task to check NST profile status."""
        try:
            logging.info(f"Starting individual profile check for {profile_id}")

            # Ensure clean state before check
            close_nst_browser_safely(profile_id)
            time.sleep(2)

            result = check_nst_profile_status(profile_id)

            # Additional cleanup after check to ensure browser is fully closed
            close_nst_browser_safely(profile_id)
            time.sleep(1)

            logging.info(f"Individual profile check completed for {profile_id}")
            return result

        except Exception as e:
            logging.error(f"Error checking NST profile {profile_id}: {e}")

            # Ensure cleanup even on error
            try:
                close_nst_browser_safely(profile_id)
                time.sleep(1)
            except:
                pass

            return {"status": "error", "facebook_status": "unknown", "error": str(e)}

    def on_nst_check_finished(self, result, error, profile_id):
        """Handle NST profile check result."""
        if error:
            self.statusBar.showMessage(f"Check failed for {profile_id}", 3000)
            return

        # Update table row
        for row in range(self.nstProfilesTable.rowCount()):
            if self.nstProfilesTable.item(row, 1).text() == profile_id:  # Profile ID is now in column 1
                # Update Status
                status = result.get("status", "unknown")
                status_item = self.nstProfilesTable.item(row, 3)  # Status is now in column 3
                status_item.setText(status.title())

                if status == "online":
                    status_item.setForeground(QColor("#4CAF50"))
                elif status == "offline":
                    status_item.setForeground(QColor("#F44336"))
                else:
                    status_item.setForeground(QColor("#FF9800"))

                # Update Facebook ID if available
                facebook_id = result.get("facebook_id")
                if facebook_id:
                    fb_id_item = self.nstProfilesTable.item(row, 2)  # Facebook ID is in column 2
                    if fb_id_item:
                        fb_id_item.setText(facebook_id)
                        fb_id_item.setForeground(QColor("#2196F3"))  # Blue color for ID

                # Update Facebook Status
                fb_status = result.get("facebook_status", "unknown")
                fb_status_item = self.nstProfilesTable.item(row, 4)  # Facebook Status is now in column 4

                # Display "Live" instead of "Active" for better clarity
                display_status = fb_status.replace("_", " ").title()
                if fb_status == "live":
                    display_status = "Live"

                fb_status_item.setText(display_status)

                if fb_status == "live":
                    fb_status_item.setForeground(QColor("#4CAF50"))
                elif fb_status == "banned":
                    fb_status_item.setForeground(QColor("#F44336"))
                elif fb_status == "not_logged_in":
                    fb_status_item.setForeground(QColor("#FF9800"))
                elif fb_status == "checkpoint":
                    fb_status_item.setForeground(QColor("#FF5722"))
                else:
                    fb_status_item.setForeground(QColor("#9E9E9E"))

                break

        self.statusBar.showMessage(f"Profile {profile_id}: {status} - Facebook: {fb_status}", 5000)



    def extract_profile_data(self, profile_id):
        """Extract pages and groups from NST profile."""
        reply = QMessageBox.question(self, "Extract Data",
                                   f"Extract Facebook Pages and Groups from profile {profile_id}?\n\n"
                                   "This will:\n"
                                   "1. Connect to the NST Browser profile\n"
                                   "2. Extract managed pages and groups\n"
                                   "3. Add them to Pages and Groups sections\n\n"
                                   "Make sure Facebook is logged in for this profile.",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        self.statusBar.showMessage(f"Extracting data from profile {profile_id}...")

        # Run in background thread
        self.nst_extract_worker = BaseWorker(self._extract_profile_data_task, profile_id)
        self.nst_extract_worker.finished.connect(lambda result, error: self.on_extract_finished(result, error, profile_id))
        self.nst_extract_worker.start()

    def _extract_profile_data_task(self, profile_id):
        """Background task to extract profile data."""
        try:
            return extract_facebook_pages_and_groups(profile_id)
        except Exception as e:
            logging.error(f"Error extracting data from profile {profile_id}: {e}")
            return {"success": False, "error": str(e), "pages": [], "groups": [], "profile_id": profile_id}

    def on_extract_finished(self, result, error, profile_id):
        """Handle profile data extraction result."""
        if error or not result or not result.get("success"):
            error_msg = error or result.get("error", "Unknown error")
            QMessageBox.critical(self, "Extraction Failed",
                               f"Failed to extract data from profile {profile_id}:\n{error_msg}")
            self.statusBar.showMessage(f"Extraction failed for {profile_id}", 3000)
            return

        pages = result.get("pages", [])
        groups = result.get("groups", [])

        if not pages and not groups:
            QMessageBox.information(self, "No Data Found",
                                  f"No pages or groups found for profile {profile_id}.\n\n"
                                  "This could mean:\n"
                                  "1. The account doesn't manage any pages/groups\n"
                                  "2. Facebook's layout has changed\n"
                                  "3. The account needs to be logged in")
            self.statusBar.showMessage(f"No data found for {profile_id}", 3000)
            return

        # Show extraction results popup for user selection
        self.show_extraction_results(profile_id, pages, groups)



    def ensure_nst_profile_as_account(self, profile_id):
        """Ensure NST profile exists as an account in the database."""
        try:
            # Check if account with this NST profile already exists
            existing = c.execute(
                "SELECT id FROM accounts WHERE nst_profile_id = ?",
                (profile_id,)
            ).fetchone()

            if not existing:
                # Create a basic account entry for this NST profile
                fb_id = f"nst_{profile_id}_{random.randint(1000, 9999)}"
                full_name = f"NST Profile {profile_id}"

                c.execute("""
                    INSERT INTO accounts (fb_id, full_name, nst_profile_id, browser_type, status)
                    VALUES (?, ?, ?, 'nst', 'live')
                """, (fb_id, full_name, profile_id))

                logging.info(f"Created account entry for NST profile {profile_id}")

                # Reload accounts data to include the new account
                self.load_accounts_data()

        except sqlite3.Error as e:
            logging.error(f"Error ensuring NST profile as account: {e}")

    def _save_profile_with_facebook_id(self, profile_id, facebook_id):
        """Save NST profile with Facebook ID to database."""
        try:
            with QMutexLocker(db_mutex):
                # Check if account with this NST profile already exists
                existing = c.execute(
                    "SELECT id, fb_id FROM accounts WHERE nst_profile_id = ?",
                    (profile_id,)
                ).fetchone()

                if existing:
                    # Update existing account with Facebook ID
                    c.execute("""
                        UPDATE accounts SET fb_id = ? WHERE nst_profile_id = ?
                    """, (facebook_id, profile_id))
                    logging.info(f"Updated NST profile {profile_id} with Facebook ID {facebook_id}")
                else:
                    # Create new account entry
                    full_name = f"NST Profile {profile_id}"
                    c.execute("""
                        INSERT INTO accounts (fb_id, full_name, nst_profile_id, browser_type, status)
                        VALUES (?, ?, ?, 'nst', 'live')
                    """, (facebook_id, full_name, profile_id))
                    logging.info(f"Created account entry for NST profile {profile_id} with Facebook ID {facebook_id}")

                conn.commit()

        except sqlite3.Error as e:
            logging.error(f"Error saving NST profile with Facebook ID: {e}")

    def show_extraction_results(self, profile_id, pages, groups):
        """Show extraction results and allow user to select what to add."""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"Extracted Data from {profile_id}")
        dialog.setModal(True)
        dialog.resize(600, 500)

        layout = QVBoxLayout(dialog)

        # Header
        header = QLabel(f"Extracted IDs from NST Profile: {profile_id}")
        header.setStyleSheet("font-size: 14px; font-weight: bold; color: #2196F3; margin-bottom: 10px;")
        layout.addWidget(header)

        # Info label
        info_label = QLabel("✓ Only pages and groups that you manage are extracted.\n"
                           "✓ Select the items you want to add to your Pages/Groups sections for scheduling.")
        info_label.setStyleSheet("color: #666; font-size: 11px; margin-bottom: 10px; padding: 5px; background-color: #f0f8ff; border-radius: 3px;")
        layout.addWidget(info_label)

        # Tabs for pages and groups
        tabs = QTabWidget()
        # Make tabs expand to fill available space and show full text
        tabs.tabBar().setExpanding(True)
        tabs.tabBar().setUsesScrollButtons(False)
        tabs.tabBar().setElideMode(Qt.ElideNone)  # Don't truncate text

        # Pages tab
        if pages:
            pages_widget = QWidget()
            pages_layout = QVBoxLayout(pages_widget)

            pages_label = QLabel(f"Found {len(pages)} Managed Facebook Pages:")
            pages_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
            pages_layout.addWidget(pages_label)

            pages_table = QTableWidget()
            pages_table.setColumnCount(4)
            pages_table.setHorizontalHeaderLabels(["Select", "Page Name", "Page ID", "URL"])
            pages_table.setRowCount(len(pages))

            for row, page in enumerate(pages):
                # Checkbox
                checkbox = QCheckBox()
                checkbox.setChecked(True)
                pages_table.setCellWidget(row, 0, checkbox)

                # Page Name
                page_name = page.get('name', page.get('page_id', 'Unknown'))
                page_name_item = QTableWidgetItem(page_name)
                pages_table.setItem(row, 1, page_name_item)

                # Page ID
                page_id_item = QTableWidgetItem(page.get('page_id', 'Unknown'))
                pages_table.setItem(row, 2, page_id_item)

                # URL
                url_item = QTableWidgetItem(page.get('url', ''))
                pages_table.setItem(row, 3, url_item)

            pages_table.resizeColumnsToContents()
            pages_layout.addWidget(pages_table)

            tabs.addTab(pages_widget, f"Pages ({len(pages)})")

        # Groups tab
        if groups:
            groups_widget = QWidget()
            groups_layout = QVBoxLayout(groups_widget)

            groups_label = QLabel(f"Found {len(groups)} Managed Facebook Groups:")
            groups_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
            groups_layout.addWidget(groups_label)

            groups_table = QTableWidget()
            groups_table.setColumnCount(4)
            groups_table.setHorizontalHeaderLabels(["Select", "Group Name", "Group ID", "URL"])
            groups_table.setRowCount(len(groups))

            for row, group in enumerate(groups):
                # Checkbox
                checkbox = QCheckBox()
                checkbox.setChecked(True)
                groups_table.setCellWidget(row, 0, checkbox)

                # Group Name
                group_name = group.get('name', group.get('group_id', 'Unknown'))
                group_name_item = QTableWidgetItem(group_name)
                groups_table.setItem(row, 1, group_name_item)

                # Group ID
                group_id_item = QTableWidgetItem(group.get('group_id', 'Unknown'))
                groups_table.setItem(row, 2, group_id_item)

                # URL
                url_item = QTableWidgetItem(group.get('url', ''))
                groups_table.setItem(row, 3, url_item)

            groups_table.resizeColumnsToContents()
            groups_layout.addWidget(groups_table)

            tabs.addTab(groups_widget, f"Groups ({len(groups)})")

        layout.addWidget(tabs)

        # Buttons
        buttons_layout = QHBoxLayout()

        select_all_btn = QPushButton("Select All")
        select_all_btn.clicked.connect(lambda: self._set_all_extraction_checkboxes(tabs, True))
        buttons_layout.addWidget(select_all_btn)

        deselect_all_btn = QPushButton("Deselect All")
        deselect_all_btn.clicked.connect(lambda: self._set_all_extraction_checkboxes(tabs, False))
        buttons_layout.addWidget(deselect_all_btn)

        buttons_layout.addStretch()

        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_btn)

        add_btn = QPushButton("Add Selected")
        add_btn.clicked.connect(lambda: self._add_selected_extraction_data(dialog, tabs, pages, groups, profile_id))
        add_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        buttons_layout.addWidget(add_btn)

        layout.addLayout(buttons_layout)

        dialog.exec_()

    def _set_all_extraction_checkboxes(self, tabs, checked):
        """Set all checkboxes in extraction dialog."""
        for i in range(tabs.count()):
            widget = tabs.widget(i)
            table = widget.findChild(QTableWidget)
            if table:
                for row in range(table.rowCount()):
                    checkbox = table.cellWidget(row, 0)
                    if checkbox:
                        checkbox.setChecked(checked)

    def _add_selected_extraction_data(self, dialog, tabs, pages, groups, profile_id):
        """Add selected pages and groups to database."""
        selected_pages = []
        selected_groups = []

        # Get selected pages
        if pages:
            pages_widget = None
            for i in range(tabs.count()):
                if "Pages" in tabs.tabText(i):
                    pages_widget = tabs.widget(i)
                    break

            if pages_widget:
                pages_table = pages_widget.findChild(QTableWidget)
                for row in range(pages_table.rowCount()):
                    checkbox = pages_table.cellWidget(row, 0)
                    if checkbox and checkbox.isChecked():
                        selected_pages.append(pages[row])

        # Get selected groups
        if groups:
            groups_widget = None
            for i in range(tabs.count()):
                if "Groups" in tabs.tabText(i):
                    groups_widget = tabs.widget(i)
                    break

            if groups_widget:
                groups_table = groups_widget.findChild(QTableWidget)
                for row in range(groups_table.rowCount()):
                    checkbox = groups_table.cellWidget(row, 0)
                    if checkbox and checkbox.isChecked():
                        selected_groups.append(groups[row])

        if not selected_pages and not selected_groups:
            QMessageBox.warning(dialog, "No Selection", "Please select at least one page or group to add.")
            return

        # Add to database
        try:
            with QMutexLocker(db_mutex):
                added_pages = 0
                added_groups = 0

                # Ensure the NST profile exists as an account
                if profile_id:
                    self.ensure_nst_profile_as_account(profile_id)

                # Add pages with proper names
                for page in selected_pages:
                    try:
                        page_id = page.get('page_id', '')
                        page_name = page.get('name', page_id)
                        page_url = page.get('url', '')
                        if page_id:
                            c.execute("""
                                INSERT OR IGNORE INTO pages (name, page_id, url, source)
                                VALUES (?, ?, ?, ?)
                            """, (page_name, page_id, page_url, 'NST Browser'))
                            if c.rowcount > 0:
                                added_pages += 1
                    except Exception as e:
                        logging.error(f"Error adding page {page.get('name', 'Unknown')}: {e}")

                # Add groups with proper names
                for group in selected_groups:
                    try:
                        group_id = group.get('group_id', '')
                        group_name = group.get('name', group_id)
                        group_url = group.get('url', '')
                        if group_id:
                            c.execute("""
                                INSERT OR IGNORE INTO groups (name, group_id, url, source)
                                VALUES (?, ?, ?, ?)
                            """, (group_name, group_id, group_url, 'NST Browser'))
                            if c.rowcount > 0:
                                added_groups += 1
                    except Exception as e:
                        logging.error(f"Error adding group {group.get('name', 'Unknown')}: {e}")

                conn.commit()

            # Show success message
            message = f"✅ Successfully added to your Pages/Groups sections:\n\n"
            if added_pages > 0:
                message += f"📄 {added_pages} Facebook Pages\n"
            if added_groups > 0:
                message += f"👥 {added_groups} Facebook Groups\n"

            if added_pages == 0 and added_groups == 0:
                message = "ℹ️ All selected items were already in the database.\n\nYou can now use them for scheduling posts."
            else:
                message += f"\n🎯 You can now use these pages/groups for scheduling posts!"

            QMessageBox.information(dialog, "Data Added", message)

            # Refresh data
            self.load_pages_data()
            self.load_groups_data()

            dialog.accept()

        except Exception as e:
            logging.error(f"Error adding extraction data: {e}")
            QMessageBox.critical(dialog, "Database Error", f"Error adding data to database:\n{e}")

    def check_all_nst_status(self):
        """Check status of selected NST profiles."""
        if self.nstProfilesTable.rowCount() == 0:
            QMessageBox.information(self, "No Profiles", "No NST profiles in the list. Add some profiles first.")
            return

        # Collect selected profile IDs
        selected_profile_ids = []
        for row in range(self.nstProfilesTable.rowCount()):
            widget = self.nstProfilesTable.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    profile_id = self.nstProfilesTable.item(row, 1).text()  # Profile ID is now in column 1
                    selected_profile_ids.append(profile_id)

        if not selected_profile_ids:
            QMessageBox.information(self, "No Selection", "Please select at least one profile to check.\n\n"
                                  "Use the checkboxes to select profiles, or use 'Select All Profiles' button.")
            return

        reply = QMessageBox.question(self, "Check Selected Status",
                                   f"Check status of {len(selected_profile_ids)} selected NST profiles?\n\n"
                                   "This will check profiles sequentially and may take some time...",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        # Reset cancellation flag and setup UI
        self.check_all_cancelled = False
        self.checkAllNSTBtn.setVisible(False)
        self.cancelCheckAllBtn.setVisible(True)

        self.statusBar.showMessage(f"Checking status of {len(selected_profile_ids)} selected profiles...")

        # Run in background thread
        self.nst_check_all_worker = BaseWorker(self._check_all_nst_profiles_task, selected_profile_ids)
        self.nst_check_all_worker.finished.connect(self.on_check_all_finished)
        self.nst_check_all_worker.start()

    def _check_all_nst_profiles_task(self, profile_ids):
        """Background task to check all NST profiles sequentially with real-time updates."""
        results = {}
        total_profiles = len(profile_ids)

        logging.info(f"Starting sequential check of {total_profiles} profiles")

        for index, profile_id in enumerate(profile_ids, 1):
            # Check if operation was cancelled
            if self.check_all_cancelled:
                logging.info(f"Check all operation cancelled at profile {index}/{total_profiles}")
                break

            try:
                logging.info(f"Checking profile {index}/{total_profiles}: {profile_id}")

                # Update status bar with current progress
                self.statusBar.showMessage(f"Checking profile {index}/{total_profiles}: {profile_id}...")

                # Ensure clean state before each check
                close_nst_browser_safely(profile_id)
                time.sleep(2)

                # Check if cancelled before starting actual check
                if self.check_all_cancelled:
                    break

                result = check_nst_profile_status(profile_id)
                results[profile_id] = result

                status = result.get('status', 'unknown')
                fb_status = result.get('facebook_status', 'unknown')
                logging.info(f"Profile {profile_id} ({index}/{total_profiles}): {status} - {fb_status}")

                # Update UI immediately after each check (thread-safe)
                QApplication.processEvents()  # Process pending events
                self.update_single_profile_result(profile_id, result)

                # Additional cleanup between checks to ensure browser is fully closed
                close_nst_browser_safely(profile_id)

                # Check if cancelled before delay
                if self.check_all_cancelled:
                    break

                time.sleep(3)  # Longer delay between checks for stability

            except Exception as e:
                logging.error(f"Error checking profile {profile_id} ({index}/{total_profiles}): {e}")
                results[profile_id] = {"status": "error", "facebook_status": "unknown", "error": str(e)}

                # Update UI with error result (thread-safe)
                QApplication.processEvents()  # Process pending events
                self.update_single_profile_result(profile_id, results[profile_id])

                # Ensure cleanup even on error
                try:
                    close_nst_browser_safely(profile_id)
                    time.sleep(2)
                except:
                    pass

        logging.info(f"Completed sequential check of {total_profiles} profiles")
        return results

    def update_single_profile_result(self, profile_id, result):
        """Update UI immediately after checking a single profile."""
        try:
            # Find the row for this profile and update it
            for row in range(self.nstProfilesTable.rowCount()):
                if self.nstProfilesTable.item(row, 1).text() == profile_id:  # Profile ID is in column 1
                    # Update Status
                    status = result.get("status", "unknown")
                    status_item = self.nstProfilesTable.item(row, 3)  # Status is in column 3
                    status_item.setText(status.title())

                    if status == "online":
                        status_item.setForeground(QColor("#4CAF50"))
                    elif status == "offline":
                        status_item.setForeground(QColor("#F44336"))
                    else:
                        status_item.setForeground(QColor("#FF9800"))

                    # Update Facebook Status
                    fb_status = result.get("facebook_status", "unknown")
                    fb_status_item = self.nstProfilesTable.item(row, 4)  # Facebook Status is in column 4

                    # Display "Live" instead of "Active" for better clarity
                    display_status = fb_status.replace("_", " ").title()
                    if fb_status == "live":
                        display_status = "Live"

                    fb_status_item.setText(display_status)

                    if fb_status == "live":
                        fb_status_item.setForeground(QColor("#4CAF50"))
                    elif fb_status == "banned":
                        fb_status_item.setForeground(QColor("#F44336"))
                    elif fb_status == "not_logged_in":
                        fb_status_item.setForeground(QColor("#FF9800"))
                    elif fb_status == "checkpoint":
                        fb_status_item.setForeground(QColor("#FF5722"))
                    elif fb_status == "error":
                        fb_status_item.setForeground(QColor("#9C27B0"))
                    else:
                        fb_status_item.setForeground(QColor("#9E9E9E"))

                    # Force table to update display
                    self.nstProfilesTable.viewport().update()
                    break

        except Exception as e:
            logging.error(f"Error updating UI for profile {profile_id}: {e}")

    def on_check_all_finished(self, results, error):
        """Handle check all profiles result."""
        # Reset UI state
        self.checkAllNSTBtn.setVisible(True)
        self.cancelCheckAllBtn.setVisible(False)
        self.checkAllNSTBtn.setEnabled(True)

        if error:
            QMessageBox.critical(self, "Check Failed", f"Error checking profiles:\n{error}")
            self.statusBar.showMessage("Check all failed", 3000)
            return

        # Check if operation was cancelled
        if self.check_all_cancelled:
            checked_count = len(results)
            self.statusBar.showMessage(f"Check cancelled after {checked_count} profiles", 5000)
            QMessageBox.information(self, "Check Cancelled",
                                  f"Status check was cancelled.\n\n"
                                  f"Checked {checked_count} profiles before cancellation.")
            return

        # Show summary (UI was already updated in real-time)
        online_count = sum(1 for r in results.values() if r.get("status") == "online")
        live_count = sum(1 for r in results.values() if r.get("facebook_status") == "live")

        QMessageBox.information(self, "Check Complete",
                              f"Status check completed for {len(results)} selected profiles!\n\n"
                              f"Online profiles: {online_count}/{len(results)}\n"
                              f"Live Facebook accounts: {live_count}/{len(results)}")

        self.statusBar.showMessage(f"Check complete: {online_count} online, {live_count} live", 5000)

    def delete_selected_nst_profiles(self):
        """Delete selected NST profiles from the table."""
        if self.nstProfilesTable.rowCount() == 0:
            QMessageBox.information(self, "No Profiles", "No NST profiles in the list.")
            return

        # Collect selected profile IDs and their row indices
        selected_profiles = []
        selected_rows = []
        for row in range(self.nstProfilesTable.rowCount()):
            widget = self.nstProfilesTable.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    profile_id = self.nstProfilesTable.item(row, 1).text()  # Profile ID is in column 1
                    selected_profiles.append(profile_id)
                    selected_rows.append(row)

        if not selected_profiles:
            QMessageBox.information(self, "No Selection", "Please select at least one profile to delete.\n\n"
                                  "Use the checkboxes to select profiles, or use 'Select All Profiles' button.")
            return

        # Confirm deletion
        reply = QMessageBox.question(self, "Delete Selected Profiles",
                                   f"Are you sure you want to delete {len(selected_profiles)} selected profiles?\n\n"
                                   f"Profiles to delete:\n" +
                                   "\n".join([f"• {pid}" for pid in selected_profiles[:5]]) +
                                   (f"\n• ... and {len(selected_profiles) - 5} more" if len(selected_profiles) > 5 else "") +
                                   f"\n\nThis action cannot be undone.",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        # Delete rows in reverse order to maintain correct indices
        selected_rows.sort(reverse=True)
        for row in selected_rows:
            self.nstProfilesTable.removeRow(row)

        # Show success message
        QMessageBox.information(self, "Deleted Successfully",
                              f"Successfully deleted {len(selected_profiles)} profiles from the list!")

        self.statusBar.showMessage(f"Deleted {len(selected_profiles)} profiles from the list", 5000)

    def cancel_check_all_nst_status(self):
        """Cancel the ongoing check all status operation."""
        self.check_all_cancelled = True

        if self.nst_check_all_worker and self.nst_check_all_worker.isRunning():
            logging.info("User requested to cancel check all status operation")
            self.statusBar.showMessage("Cancelling check all status...", 3000)

            # Try to terminate the worker thread gracefully
            try:
                self.nst_check_all_worker.terminate()
                self.nst_check_all_worker.wait(3000)  # Wait up to 3 seconds
            except:
                pass

        # Reset UI state
        self.checkAllNSTBtn.setVisible(True)
        self.cancelCheckAllBtn.setVisible(False)
        self.checkAllNSTBtn.setEnabled(True)

        # Cleanup any remaining processes
        try:
            kill_nstchrome_processes()
        except:
            pass

        self.statusBar.showMessage("Check all status cancelled", 3000)
        logging.info("Check all status operation cancelled by user")













    def add_profiles_to_list_popup(self):
        """Add NST profile IDs to the list via popup dialog."""
        # Create input dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("Add NST Profile IDs")
        dialog.setModal(True)
        dialog.resize(600, 500)

        layout = QVBoxLayout(dialog)

        # Header
        header = QLabel("Add NST Profile IDs")
        header.setStyleSheet("font-size: 14px; font-weight: bold; color: #4CAF50; margin-bottom: 10px;")
        layout.addWidget(header)

        # Instructions
        instructions = QLabel(
            "Enter NST Browser Profile IDs and their corresponding Facebook IDs.\n"
            "You can add profiles in two ways:\n"
            "1. Profile ID only (Facebook ID will be extracted automatically)\n"
            "2. Profile ID with Facebook ID (format: profile_id:facebook_id)"
        )
        instructions.setStyleSheet("color: #666; margin-bottom: 10px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Profile IDs input
        profile_label = QLabel("NST Profile IDs:")
        profile_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(profile_label)

        profile_edit = QTextEdit()
        profile_edit.setPlaceholderText(
            "Enter NST Browser Profile IDs (one per line):\n\n"
            "Format 1 (Profile ID only - Facebook ID will be extracted):\n"
            "profile_001\n"
            "profile_002\n\n"
            "Format 2 (Profile ID with Facebook ID):\n"
            "profile_003:***************\n"
            "profile_004:***************\n\n"
            "# Comment: Special accounts\n"
            "profile_005\n"
            "..."
        )
        profile_edit.setMaximumHeight(250)
        layout.addWidget(profile_edit)

        # Buttons
        buttons_layout = QHBoxLayout()

        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_btn)

        add_btn = QPushButton("Add to List")
        add_btn.clicked.connect(lambda: self._process_profile_list_popup(dialog, profile_edit.toPlainText()))
        add_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        buttons_layout.addWidget(add_btn)

        layout.addLayout(buttons_layout)

        dialog.exec_()



    def _process_profile_list_popup(self, dialog, profile_text):
        """Process profile list from popup dialog."""
        profile_text = profile_text.strip()
        if not profile_text:
            QMessageBox.warning(dialog, "No Input", "Please enter at least one NST Profile ID.")
            return

        # Parse profile IDs and Facebook IDs
        profile_data = []
        for line in profile_text.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):  # Allow comments with #
                # Check if line contains Facebook ID (format: profile_id:facebook_id)
                if ':' in line:
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        profile_id = parts[0].strip()
                        facebook_id = parts[1].strip()
                        if profile_id and facebook_id:
                            profile_data.append({
                                'profile_id': profile_id,
                                'facebook_id': facebook_id,
                                'has_facebook_id': True
                            })
                else:
                    # Profile ID only
                    profile_id = line.strip()
                    if profile_id:
                        profile_data.append({
                            'profile_id': profile_id,
                            'facebook_id': None,
                            'has_facebook_id': False
                        })

        if not profile_data:
            QMessageBox.warning(dialog, "No Valid IDs", "No valid Profile IDs found.")
            return

        # Close dialog
        dialog.accept()

        # Check for duplicates in current table
        existing_profiles = []
        profile_ids = [data['profile_id'] for data in profile_data]

        for row in range(self.nstProfilesTable.rowCount()):
            existing_id = self.nstProfilesTable.item(row, 1).text()  # Profile ID is now in column 1
            if existing_id in profile_ids:
                existing_profiles.append(existing_id)

        # Remove duplicates
        new_profile_data = [data for data in profile_data if data['profile_id'] not in existing_profiles]

        if not new_profile_data and existing_profiles:
            QMessageBox.information(self, "All Exist",
                                  f"All {len(profile_data)} Profile IDs are already in the list.")
            return

        # Show confirmation
        message = f"Add {len(profile_data)} Profile IDs to the list?\n\n"
        if new_profile_data:
            message += f"✅ New profiles: {len(new_profile_data)}\n"
            for data in new_profile_data[:5]:
                fb_info = f" (FB: {data['facebook_id']})" if data['has_facebook_id'] else " (FB: Auto-extract)"
                message += f"   • {data['profile_id']}{fb_info}\n"
            if len(new_profile_data) > 5:
                message += f"   • ... and {len(new_profile_data) - 5} more\n"

        if existing_profiles:
            message += f"\n⚠️ Already in list: {len(existing_profiles)}\n"
            for pid in existing_profiles[:3]:
                message += f"   • {pid}\n"
            if len(existing_profiles) > 3:
                message += f"   • ... and {len(existing_profiles) - 3} more\n"

        reply = QMessageBox.question(self, "Add to List", message,
                                   QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        # Add new profiles to table
        for profile_info in new_profile_data:
            profile_id = profile_info['profile_id']
            facebook_id = profile_info['facebook_id']
            has_facebook_id = profile_info['has_facebook_id']

            row = self.nstProfilesTable.rowCount()
            self.nstProfilesTable.insertRow(row)

            # Checkbox
            checkbox = QCheckBox()
            checkbox.setChecked(False)  # Default unchecked
            cb_container = QWidget()
            cb_layout = QHBoxLayout(cb_container)
            cb_layout.addWidget(checkbox, alignment=Qt.AlignCenter)
            cb_layout.setContentsMargins(0, 0, 0, 0)
            self.nstProfilesTable.setCellWidget(row, 0, cb_container)

            # Profile ID
            self.nstProfilesTable.setItem(row, 1, QTableWidgetItem(profile_id))

            # Facebook ID
            if has_facebook_id and facebook_id:
                self.nstProfilesTable.setItem(row, 2, QTableWidgetItem(facebook_id))
                # Also save to database if provided
                self._save_profile_with_facebook_id(profile_id, facebook_id)
            else:
                self.nstProfilesTable.setItem(row, 2, QTableWidgetItem("Unknown"))
                # Save profile without Facebook ID
                self._save_profile_without_facebook_id(profile_id)

            # Status
            status_item = QTableWidgetItem("Not Checked")
            status_item.setForeground(QColor("#FF9800"))  # Orange
            self.nstProfilesTable.setItem(row, 3, status_item)

            # Facebook Status
            fb_status_item = QTableWidgetItem("Unknown")
            fb_status_item.setForeground(QColor("#9E9E9E"))  # Gray
            self.nstProfilesTable.setItem(row, 4, fb_status_item)

            # Actions buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(4, 4, 4, 4)
            actions_layout.setSpacing(2)

            check_btn = QPushButton("Check")
            check_btn.setMinimumWidth(80)
            check_btn.setMaximumWidth(80)
            check_btn.setMinimumHeight(25)
            check_btn.setStyleSheet("font-size: 10px; padding: 2px;")
            check_btn.clicked.connect(lambda checked, pid=profile_id: self.check_nst_profile_status(pid))
            actions_layout.addWidget(check_btn)

            self.nstProfilesTable.setCellWidget(row, 5, actions_widget)

            # Manage Pages/Groups button
            manage_widget = QWidget()
            manage_layout = QHBoxLayout(manage_widget)
            manage_layout.setContentsMargins(4, 4, 4, 4)
            manage_layout.setSpacing(2)

            manage_btn = QPushButton("Manage")
            manage_btn.setMinimumWidth(80)
            manage_btn.setMaximumWidth(80)
            manage_btn.setMinimumHeight(25)
            manage_btn.setStyleSheet("font-size: 10px; padding: 2px; background-color: #4CAF50; color: white;")
            manage_btn.clicked.connect(lambda checked, pid=profile_id: self.manage_profile_pages_groups(pid))
            manage_layout.addWidget(manage_btn)

            self.nstProfilesTable.setCellWidget(row, 6, manage_widget)

        # Show success message
        QMessageBox.information(self, "Added Successfully",
                              f"Successfully added {len(new_profile_data)} Profile IDs to the list!\n\n"
                              f"You can now:\n"
                              f"• Check their status\n"
                              f"• Extract their data")

        self.statusBar.showMessage(f"Added {len(new_profile_data)} Profile IDs to the list", 5000)

    def _save_profile_with_facebook_id(self, profile_id, facebook_id):
        """Save NST profile with provided Facebook ID to database."""
        try:
            with QMutexLocker(db_mutex):
                # Check if account with this NST profile already exists
                existing = c.execute(
                    "SELECT id, fb_id FROM accounts WHERE nst_profile_id = ?",
                    (profile_id,)
                ).fetchone()

                if existing:
                    # Update existing account with provided Facebook ID
                    c.execute(
                        "UPDATE accounts SET fb_id = ? WHERE nst_profile_id = ?",
                        (facebook_id, profile_id)
                    )
                    logging.info(f"Updated Facebook ID for NST profile {profile_id}: {facebook_id}")
                else:
                    # Create new account with provided Facebook ID
                    full_name = f"NST Profile {profile_id}"
                    c.execute("""
                        INSERT INTO accounts (fb_id, full_name, nst_profile_id, browser_type, status)
                        VALUES (?, ?, ?, 'nst', 'live')
                    """, (facebook_id, full_name, profile_id))
                    logging.info(f"Created account for NST profile {profile_id} with Facebook ID: {facebook_id}")

                conn.commit()

                # Reload accounts data to include the new/updated account
                self.load_accounts_data()

        except sqlite3.Error as e:
            logging.error(f"Error saving profile with Facebook ID: {e}")

    def _save_profile_without_facebook_id(self, profile_id):
        """Save NST profile without Facebook ID to database."""
        try:
            with QMutexLocker(db_mutex):
                # Check if account with this NST profile already exists
                existing = c.execute(
                    "SELECT id FROM accounts WHERE nst_profile_id = ?",
                    (profile_id,)
                ).fetchone()

                if not existing:
                    # Create new account entry with generated Facebook ID
                    fb_id = f"nst_{profile_id}_{random.randint(1000, 9999)}"
                    full_name = f"NST Profile {profile_id}"
                    c.execute("""
                        INSERT INTO accounts (fb_id, full_name, nst_profile_id, browser_type, status)
                        VALUES (?, ?, ?, 'nst', 'live')
                    """, (fb_id, full_name, profile_id))
                    logging.info(f"Created account for NST profile {profile_id}")

                    conn.commit()

                    # Reload accounts data to include the new account
                    self.load_accounts_data()

        except sqlite3.Error as e:
            logging.error(f"Error saving profile without Facebook ID: {e}")

    def load_saved_nst_profiles(self):
        """Load saved NST profiles from database and populate the table."""
        try:
            with QMutexLocker(db_mutex):
                # Get all NST Browser accounts
                c.execute("""
                    SELECT nst_profile_id, fb_id, full_name, status
                    FROM accounts
                    WHERE nst_profile_id IS NOT NULL AND browser_type = 'nst'
                    ORDER BY nst_profile_id
                """)
                rows = c.fetchall()

                if not rows:
                    return

                # Clear existing table
                self.nstProfilesTable.setRowCount(0)

                for row_data in rows:
                    profile_id, fb_id, full_name, status = row_data

                    row = self.nstProfilesTable.rowCount()
                    self.nstProfilesTable.insertRow(row)

                    # Checkbox
                    checkbox = QCheckBox()
                    checkbox.setChecked(False)
                    cb_container = QWidget()
                    cb_layout = QHBoxLayout(cb_container)
                    cb_layout.addWidget(checkbox, alignment=Qt.AlignCenter)
                    cb_layout.setContentsMargins(0, 0, 0, 0)
                    self.nstProfilesTable.setCellWidget(row, 0, cb_container)

                    # Profile ID
                    self.nstProfilesTable.setItem(row, 1, QTableWidgetItem(profile_id))

                    # Facebook ID
                    if fb_id and not fb_id.startswith('nst_'):
                        self.nstProfilesTable.setItem(row, 2, QTableWidgetItem(fb_id))
                    else:
                        self.nstProfilesTable.setItem(row, 2, QTableWidgetItem("Unknown"))

                    # Status
                    status_item = QTableWidgetItem("Not Checked")
                    status_item.setForeground(QColor("#FF9800"))  # Orange
                    self.nstProfilesTable.setItem(row, 3, status_item)

                    # Facebook Status
                    fb_status_item = QTableWidgetItem("Unknown")
                    fb_status_item.setForeground(QColor("#9E9E9E"))  # Gray
                    self.nstProfilesTable.setItem(row, 4, fb_status_item)

                    # Actions buttons
                    actions_widget = QWidget()
                    actions_layout = QHBoxLayout(actions_widget)
                    actions_layout.setContentsMargins(4, 4, 4, 4)
                    actions_layout.setSpacing(2)

                    check_btn = QPushButton("Check")
                    check_btn.setMinimumWidth(80)
                    check_btn.setMaximumWidth(80)
                    check_btn.setMinimumHeight(25)
                    check_btn.setStyleSheet("font-size: 10px; padding: 2px;")
                    check_btn.clicked.connect(lambda checked, pid=profile_id: self.check_nst_profile_status(pid))
                    actions_layout.addWidget(check_btn)

                    self.nstProfilesTable.setCellWidget(row, 5, actions_widget)

                    # Manage Pages/Groups button
                    manage_widget = QWidget()
                    manage_layout = QHBoxLayout(manage_widget)
                    manage_layout.setContentsMargins(4, 4, 4, 4)
                    manage_layout.setSpacing(2)

                    manage_btn = QPushButton("Manage")
                    manage_btn.setMinimumWidth(80)
                    manage_btn.setMaximumWidth(80)
                    manage_btn.setMinimumHeight(25)
                    manage_btn.setStyleSheet("font-size: 10px; padding: 2px; background-color: #4CAF50; color: white;")
                    manage_btn.clicked.connect(lambda checked, pid=profile_id: self.manage_profile_pages_groups(pid))
                    manage_layout.addWidget(manage_btn)

                    self.nstProfilesTable.setCellWidget(row, 6, manage_widget)

                logging.info(f"Loaded {len(rows)} saved NST profiles")

        except Exception as e:
            logging.error(f"Error loading saved NST profiles: {e}")







    # End of NST Browser Manager Functions



    def open_nst_browser_for_direct_posting(self, account_data, target_info, post_content="", images=None):
        """Open NST Browser directly for manual posting without adding to posts table."""
        try:
            # Kill any existing processes
            kill_nstchrome_processes()
            time.sleep(2)

            # Launch NST Browser (NOT headless for manual interaction)
            debugger_address = launch_and_connect_to_browser(account_data.nst_profile_id, headless=False)
            if not debugger_address:
                logging.error(f"Failed to launch NST Browser for profile {account_data.nst_profile_id}")
                QMessageBox.critical(self, "خطأ", f"فشل في تشغيل متصفح NST Browser للبروفيل {account_data.nst_profile_id}")
                return False

            # Connect Selenium
            driver = exec_selenium_nst(debugger_address)
            if not driver:
                logging.error(f"Failed to connect Selenium to NST Browser")
                QMessageBox.critical(self, "خطأ", "فشل في الاتصال بمتصفح NST Browser")
                return False

            # Navigate to target URL
            target_url = self._get_target_url(target_info)
            if target_url:
                driver.get(target_url)
                time.sleep(3)

                # Check if we're logged in
                if "login" in driver.current_url.lower():
                    QMessageBox.warning(self, "تحذير", "المتصفح غير مسجل دخول إلى فيسبوك")
                    return False

                # If content is provided, try to paste it
                if post_content:
                    self._paste_content_to_facebook(driver, post_content, images)

                # Show success message
                QMessageBox.information(self, "نجح",
                    f"تم فتح متصفح NST Browser للنشر في:\n{target_info.get('name', 'الهدف المحدد')}\n\n"
                    f"يمكنك الآن النشر مباشرة في المتصفح.")

                logging.info(f"✅ Opened NST Browser for direct posting to {target_info.get('name', 'target')}")
                return True
            else:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على رابط الهدف")
                return False

        except Exception as e:
            logging.error(f"Error opening NST Browser for direct posting: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح متصفح NST Browser: {str(e)}")
            return False

    def _get_target_url(self, target_info):
        """Get the Facebook URL for the target page or group."""
        target_type = target_info.get('type', '')
        target_id = target_info.get('id', '')

        if target_type == 'page' and target_id:
            return f"https://www.facebook.com/{target_id}"
        elif target_type == 'group' and target_id:
            return f"https://www.facebook.com/groups/{target_id}"
        else:
            return "https://www.facebook.com"

    def _paste_content_to_facebook(self, driver, content, images=None):
        """Try to paste content into Facebook post composer."""
        try:
            # Wait a bit for page to load
            time.sleep(3)

            # Try to find the post composer
            post_composers = [
                "//div[@role='textbox'][@contenteditable='true']",
                "//div[@data-text='What\\'s on your mind?']",
                "//div[contains(@aria-label, 'What')]",
                "//textarea[@placeholder]"
            ]

            composer_found = False
            for xpath in post_composers:
                try:
                    composer = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, xpath))
                    )
                    composer.click()
                    time.sleep(1)
                    composer.send_keys(content)
                    composer_found = True
                    logging.info("✅ Content pasted into Facebook composer")
                    break
                except:
                    continue

            if not composer_found:
                logging.warning("Could not find Facebook post composer, content not pasted")

        except Exception as e:
            logging.error(f"Error pasting content to Facebook: {e}")

    def post_to_facebook_with_account(self, account: AccountData, post_content: str, target_page_or_group: str = None, images: list = None):
        """Post to Facebook using NST Browser for the account."""
        driver = None
        try:
            # Create NST Browser driver
            driver = create_driver_for_account(account)

            # Navigate to Facebook (already logged in via NST Browser)
            driver.get("https://www.facebook.com")
            time.sleep(3)

            # Check if we're logged in
            if "login" in driver.current_url.lower():
                raise Exception("Not logged in to Facebook")

            # Navigate to target page/group if specified
            if target_page_or_group:
                # This would need to be implemented based on the target type
                # For now, just post to timeline
                pass

            # Find the post composer
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # Wait for and click the post composer
            composer = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//div[@role='button' and contains(@aria-label, 'What')]"))
            )
            composer.click()
            time.sleep(2)

            # Find the text area and enter content
            text_area = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//div[@role='textbox']"))
            )
            text_area.send_keys(post_content)
            time.sleep(1)

            # Handle images if provided
            if images:
                # Find and click photo/video button
                photo_btn = driver.find_element(By.XPATH, "//div[@aria-label='Photo/video']")
                photo_btn.click()
                time.sleep(2)

                # Upload images
                file_input = driver.find_element(By.XPATH, "//input[@type='file']")
                for image_path in images:
                    if os.path.exists(image_path):
                        file_input.send_keys(image_path)
                        time.sleep(1)

            # Find and click the post button
            post_btn = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//div[@role='button' and @aria-label='Post']"))
            )
            post_btn.click()

            # Wait for post to be published
            time.sleep(5)

            logging.info(f"Successfully posted to Facebook using NST Browser for account {account.full_name}")
            return True

        except Exception as e:
            logging.error(f"Failed to post to Facebook for account {account.full_name}: {e}")
            raise e
        finally:
            if driver:
                try:
                    # Leave NST Browser open for future use
                    pass
                except Exception as e:
                    logging.error(f"Error with driver: {e}")

    def _find_post_creation_area(self, driver):
        """Find the post creation area using multiple selectors."""
        selectors = [
            "//div[@role='button' and contains(@aria-label, 'What')]",
            "//div[@role='textbox']",
            "//textarea[contains(@placeholder, 'mind')]",
            "//div[contains(@data-testid, 'status-attachment-mentions-input')]"
        ]

        for selector in selectors:
            try:
                element = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                return element
            except:
                continue
        return None

    def _navigate_to_target(self, driver, target):
        """Navigate to specific page or group."""
        try:
            # This is a simplified implementation
            # In a full implementation, you would parse the target type and navigate accordingly
            if target.startswith("http"):
                driver.get(target)
                time.sleep(3)
                return True
            else:
                # Search for the target
                search_url = f"https://www.facebook.com/search/top/?q={target}"
                driver.get(search_url)
                time.sleep(3)
                return True
        except Exception as e:
            logging.error(f"Error navigating to target {target}: {e}")
            return False

    def _upload_images(self, driver, images):
        """Upload images to the post."""
        try:
            # Find photo/video button
            photo_selectors = [
                "//div[@aria-label='Photo/video']",
                "//div[contains(@data-testid, 'photo-video-button')]",
                "//input[@type='file' and contains(@accept, 'image')]"
            ]

            for selector in photo_selectors:
                try:
                    if "input" in selector:
                        file_input = driver.find_element(By.XPATH, selector)
                    else:
                        photo_btn = driver.find_element(By.XPATH, selector)
                        photo_btn.click()
                        time.sleep(2)
                        file_input = driver.find_element(By.XPATH, "//input[@type='file']")

                    # Upload images
                    for image_path in images:
                        if os.path.exists(image_path):
                            file_input.send_keys(image_path)
                            time.sleep(1)
                    return True
                except:
                    continue
            return False
        except Exception as e:
            logging.error(f"Error uploading images: {e}")
            return False

    def _click_post_button(self, driver):
        """Find and click the post button."""
        selectors = [
            "//div[@role='button' and @aria-label='Post']",
            "//button[contains(@data-testid, 'react-composer-post-button')]",
            "//div[contains(@data-testid, 'react-composer-post-button')]"
        ]

        for selector in selectors:
            try:
                post_btn = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                post_btn.click()
                return True
            except:
                continue
        return False

    def _verify_post_published(self, driver):
        """Verify that the post was published successfully."""
        try:
            # Look for success indicators
            success_indicators = [
                "//div[contains(text(), 'Your post is now published')]",
                "//div[contains(text(), 'Post shared')]",
                "//span[contains(text(), 'now')]"  # "Just now" timestamp
            ]

            for indicator in success_indicators:
                try:
                    WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, indicator))
                    )
                    return True
                except:
                    continue
            return True  # Assume success if no clear failure
        except:
            return True  # Assume success if verification fails



    # --- Post Management Actions ---

    def get_current_posts_table(self) -> PostsTableWidget | None:
         """Gets the currently visible table widget in the Posts tab."""
         if hasattr(self, 'postsTabs'):
              current_widget = self.postsTabs.currentWidget()
              if isinstance(current_widget, PostsTableWidget):
                   return current_widget
         return None

    def select_all_current_posts(self):
        """Select all posts in the currently visible table."""
        current_table = self.get_current_posts_table()
        if current_table:
            current_table.select_all_posts(True)

    def deselect_all_current_posts(self):
        """Deselect all posts in the currently visible table."""
        current_table = self.get_current_posts_table()
        if current_table:
            current_table.select_all_posts(False)

    def update_posts_tab_titles(self):
        """Update tab titles to include post counts."""
        if not hasattr(self, 'postsTabs') or not hasattr(self, 'all_posts_data'):
            return

        for i in range(self.postsTabs.count()):
            tab_widget = self.postsTabs.widget(i)
            if isinstance(tab_widget, PostsTableWidget):
                post_type = tab_widget.post_type
                post_count = len(self.all_posts_data.get(post_type, []))

                # Get the icon for this tab
                icon = self.postsTabs.tabIcon(i)

                # Update tab text with count
                new_title = f"{post_type} ({post_count})"
                self.postsTabs.setTabText(i, new_title)

    def open_create_post_dialog(self):
        """Opens the dialog to create a new post of the current tab's type."""
        current_table = self.get_current_posts_table()
        if not current_table:
            logging.warning("Create Post clicked but no current table found.")
            return

        post_type = current_table.post_type
        dlg = PostDialogBase(post_type=post_type, parent=self)
        # Connect the signal AFTER creating the dialog
        dlg.postSaved.connect(self.save_new_post_data)
        dlg.exec_() # Show dialog modally

    def save_new_post_data(self, post_data: PostData):
        """Receives data from Create dialog and saves to DB."""
        try:
            with QMutexLocker(db_mutex):
                cursor = c.execute('''
                    INSERT INTO posts (content, recipe_text, website_link, image_path, post_type, status, scheduled_time, account_id)
                    VALUES (?, ?, ?, ?, ?, 'pending', ?, ?)
                ''', (
                    post_data.content,
                    post_data.recipe_text,
                    post_data.website_link,
                    post_data.image_path,
                    post_data.post_type,
                    None, # scheduled_time - set later
                    None  # account_id - set later or default
                    ))
                conn.commit()
                new_post_id = cursor.lastrowid # Get the ID of the inserted post
            logging.info(f"New post created successfully (ID: {new_post_id}, Type: {post_data.post_type})")
            QMessageBox.information(self, "Success", f"{post_data.post_type} post created successfully!")
            self.statusBar.showMessage("New post created.", 4000)
            self.load_posts_data() # Refresh the posts table
        except sqlite3.Error as e:
            logging.error(f"Database error creating post: {e}")
            QMessageBox.critical(self, "Database Error", f"Could not create post: {e}")

    def edit_selected_post(self):
        """Opens the edit dialog for the single selected post."""
        selected_id = -1
        selected_post_type = ""
        found_count = 0

        # Iterate through all post tables to find the selected one
        for post_type, table in self.postsTables.items():
            ids = table.get_selected_post_ids()
            if ids:
                if found_count > 0 or len(ids) > 1: # Found selection in multiple tables or multiple rows
                    QMessageBox.warning(self, "Edit Post", "Please select only one post across all categories to edit.")
                    return
                selected_id = ids[0]
                selected_post_type = post_type
                found_count = 1

        if selected_id == -1:
            QMessageBox.information(self, "Edit Post", "No post selected for editing.")
            return

        # Find the PostData object
        post_to_edit = None
        if selected_post_type in self.all_posts_data:
            post_to_edit = next((p for p in self.all_posts_data[selected_post_type] if p.id == selected_id), None)

        if not post_to_edit:
            logging.error(f"Selected post ID {selected_id} not found in internal data for type {selected_post_type}.")
            QMessageBox.warning(self, "Edit Error", "Could not find the data for the selected post.")
            return

        # Ensure the correct tab is visible (optional, but good UX)
        if self.postsTabs.currentWidget() != self.postsTables[selected_post_type]:
             self.postsTabs.setCurrentWidget(self.postsTables[selected_post_type])

        # Open the dialog in edit mode
        dlg = PostDialogBase(post_type=selected_post_type, post_data=post_to_edit, parent=self)
        dlg.postSaved.connect(self.save_edited_post_data)
        dlg.exec_()

    def save_edited_post_data(self, post_data: PostData):
         """Receives data from Edit dialog and updates DB."""
         if post_data.id == -1:
              logging.error("Attempted to save edited post with invalid ID (-1).")
              return

         try:
            with QMutexLocker(db_mutex):
                 c.execute('''
                    UPDATE posts SET
                        content=?, recipe_text=?, website_link=?, image_path=?, post_type=?
                    WHERE id=?
                 ''', (
                    post_data.content,
                    post_data.recipe_text,
                    post_data.website_link,
                    post_data.image_path,
                    post_data.post_type, # Type generally shouldn't change, but include just in case
                    post_data.id
                 ))
                 conn.commit()
            logging.info(f"Post ID {post_data.id} updated successfully.")
            QMessageBox.information(self, "Success", "Post updated successfully!")
            self.statusBar.showMessage(f"Post {post_data.display_pid} updated.", 4000)
            self.load_posts_data() # Refresh tables
         except sqlite3.Error as e:
            logging.error(f"Database error updating post ID {post_data.id}: {e}")
            QMessageBox.critical(self, "Database Error", f"Could not update post: {e}")


    def remove_selected_posts(self):
        """Removes selected posts from DB and UI."""
        ids_to_remove = []
        # Collect IDs from all tables
        for table in self.postsTables.values():
             ids_to_remove.extend(table.get_selected_post_ids())

        if not ids_to_remove:
            QMessageBox.information(self, "Remove Posts", "No posts selected to remove.")
            return

        # Remove duplicates if collected from multiple tables (shouldn't happen with single selection)
        ids_to_remove = list(set(ids_to_remove))

        reply = QMessageBox.question(
            self,
            "Confirm Removal",
            f"Are you sure you want to remove {len(ids_to_remove)} selected post(s)?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                with QMutexLocker(db_mutex):
                    c.executemany("DELETE FROM posts WHERE id=?", [(pid,) for pid in ids_to_remove])
                    conn.commit()
                logging.info(f"Removed {len(ids_to_remove)} posts: {ids_to_remove}")
                self.statusBar.showMessage(f"{len(ids_to_remove)} post(s) removed.", 4000)
                self.load_posts_data() # Refresh UI
            except sqlite3.Error as e:
                logging.error(f"Database error removing posts: {e}")
                QMessageBox.critical(self, "Database Error", f"Could not remove posts: {e}")

    def update_post_statuses_ui(self, updated_post_ids: list[int]):
        """Updates the status column for specific posts in the UI."""
        logging.debug(f"Updating UI for post IDs: {updated_post_ids}")
        if not hasattr(self, 'postsTables'): return

        try:
            with QMutexLocker(db_mutex):
                 # Fetch new statuses for the updated posts
                 placeholders = ','.join('?' * len(updated_post_ids))
                 rows = c.execute(f"SELECT id, status, post_type FROM posts WHERE id IN ({placeholders})", updated_post_ids).fetchall()

            status_map = {row['id']: (row['status'], row['post_type']) for row in rows}

            # Update relevant tables
            for post_type, table in self.postsTables.items():
                table.setUpdatesEnabled(False)
                status_col_idx = -1
                # Find status column index dynamically
                for i, col_info in enumerate(table.post_columns):
                     if col_info['key'] == 'status':
                          status_col_idx = i
                          break
                if status_col_idx == -1: continue # Skip if no status column

                # Iterate through visible rows and update if ID matches
                for row_idx in range(table.rowCount()):
                     item = table.item(row_idx, 0) # Assuming PID is column 0
                     if item:
                          post_id = item.data(Qt.UserRole) # Get DB ID stored in UserRole
                          if post_id in status_map:
                               new_status, ptype = status_map[post_id]
                               if ptype == table.post_type: # Ensure it's the correct table
                                    status_item = table.item(row_idx, status_col_idx)
                                    if status_item:
                                         # Enhanced status display with emojis and colors
                                         if new_status == 'posted':
                                             status_text = "✅ تم النشر"
                                             status_item.setBackground(QColor("#d4edda"))  # Light green
                                             status_item.setForeground(QColor("#155724"))  # Dark green
                                         elif new_status == 'failed':
                                             status_text = "❌ فشل النشر"
                                             status_item.setBackground(QColor("#f8d7da"))  # Light red
                                             status_item.setForeground(QColor("#721c24"))  # Dark red
                                         elif new_status == 'pending':
                                             status_text = "⏳ في الانتظار"
                                             status_item.setBackground(QColor("#fff3cd"))  # Light yellow
                                             status_item.setForeground(QColor("#856404"))  # Dark yellow
                                         else:
                                             status_text = new_status
                                             status_item.setBackground(QColor("#ffffff"))  # White
                                             status_item.setForeground(QColor("#000000"))  # Black

                                         status_item.setText(status_text)
                                         status_item.setToolTip(f"حالة المنشور: {status_text}")
                                    logging.debug(f"Updated status for Post ID {post_id} to '{new_status}' in UI.")
                table.setUpdatesEnabled(True)

        except sqlite3.Error as e:
            logging.error(f"DB error fetching updated post statuses: {e}")
        except Exception as e:
             logging.error(f"Error updating post statuses UI: {e}", exc_info=True)


    # --- Schedule Management Actions ---

    def open_create_schedule_dialog(self):
        """Opens the dialog to define and generate a new schedule."""
        dlg = CreateScheduleDialog(self)
        dlg.scheduleCreated.connect(self.save_new_schedule_data)
        dlg.exec_()

    def save_new_schedule_data(self, schedule_list: list):
         """Saves the generated schedule list to the DB and adds a tab."""
         if not schedule_list:
              logging.warning("Attempted to save an empty schedule list.")
              return

         try:
              # Convert AccountData objects to dictionaries for JSON serialization
              serializable_schedule = []
              for entry in schedule_list:
                  serializable_entry = entry.copy()

                  # Convert AccountData object to dictionary if present
                  if 'account' in serializable_entry and hasattr(serializable_entry['account'], '__dict__'):
                      account_data = serializable_entry['account']
                      serializable_entry['account'] = {
                          'id': account_data.id,
                          'fb_id': account_data.fb_id,
                          'full_name': account_data.full_name,
                          'cookie': account_data.cookie,
                          'status': account_data.status,
                          'nst_profile_id': account_data.nst_profile_id,
                          'browser_type': account_data.browser_type
                      }

                  serializable_schedule.append(serializable_entry)

              schedule_json = json.dumps(serializable_schedule) # Convert list to JSON string
              with QMutexLocker(db_mutex):
                   cursor = c.execute("INSERT INTO schedules (schedule_json) VALUES (?)", (schedule_json,))
                   conn.commit()
                   new_schedule_id = cursor.lastrowid
              logging.info(f"New schedule (ID: {new_schedule_id}) saved successfully.")
              self.statusBar.showMessage("New schedule created.", 4000)

              # Add the new schedule as a tab
              # If placeholder exists, remove it first
              if self.schedulesTabWidget.tabText(0) == "No Schedules":
                    self.schedulesTabWidget.removeTab(0)
                    self.schedulesTabWidget.setTabsClosable(True) # Enable closing again

              self._add_schedule_tab(schedule_list, new_schedule_id, f"Schedule {new_schedule_id}")
              self.schedulesTabWidget.setCurrentIndex(self.schedulesTabWidget.count() - 1) # Switch to new tab

         except json.JSONDecodeError as json_err:
              logging.error(f"Failed to serialize schedule data to JSON: {json_err}")
              QMessageBox.critical(self, "Save Error", f"Could not serialize schedule data: {json_err}")
         except sqlite3.Error as db_err:
              logging.error(f"Database error saving new schedule: {db_err}")
              QMessageBox.critical(self, "Database Error", f"Could not save new schedule: {db_err}")

    def _add_schedule_tab(self, schedule_data: list, schedule_id: int, tab_name: str):
        """Adds a new tab with a table for the given schedule data."""
        tab_widget = QWidget()
        tab_layout = QVBoxLayout(tab_widget)
        tab_layout.setContentsMargins(5, 5, 5, 5)
        tab_layout.setSpacing(8)

        # --- Filters ---
        filter_widget = QWidget()
        filter_layout = QHBoxLayout(filter_widget)
        filter_layout.setContentsMargins(0, 0, 0, 0)
        filter_layout.setSpacing(10)

        # Prepare filter options based on actual data
        all_times = sorted(list(set(entry.get("time", "N/A") for entry in schedule_data)), key=lambda x: datetime.datetime.strptime(x, "%I:%M %p").time() if x != "N/A" else datetime.time.max)
        all_dates = sorted(list(set(entry.get("date", "N/A") for entry in schedule_data)))
        all_categories = sorted(list(set(entry.get("category", "N/A") for entry in schedule_data)))
        all_targets = sorted(list(set(entry.get("item_name", "N/A") for entry in schedule_data)))

        # Time Filter
        filter_layout.addWidget(QLabel("Time:"))
        time_filter_combo = QComboBox()
        time_filter_combo.addItem("All Times")
        time_filter_combo.addItems(all_times)
        filter_layout.addWidget(time_filter_combo)

        # Date Filter (Simple date selection for now)
        filter_layout.addWidget(QLabel("Date:"))
        date_filter_combo = QComboBox()
        date_filter_combo.addItem("All Dates")
        date_filter_combo.addItems(all_dates)
        filter_layout.addWidget(date_filter_combo)

        # Category Filter
        filter_layout.addWidget(QLabel("Category:"))
        category_filter_combo = QComboBox()
        category_filter_combo.addItem("All Categories")
        category_filter_combo.addItems(all_categories)
        filter_layout.addWidget(category_filter_combo)

        # Target Filter
        filter_layout.addWidget(QLabel("Target:"))
        target_filter_combo = QComboBox()
        target_filter_combo.addItem("All Targets")
        target_filter_combo.addItems(all_targets)
        filter_layout.addWidget(target_filter_combo)

        filter_layout.addStretch()
        tab_layout.addWidget(filter_widget)

        # --- Table ---
        schedule_table = QTableWidget()
        schedule_table.setColumnCount(8)
        schedule_table.setHorizontalHeaderLabels(["Post ID", "📅 Date", "⏰ Time", "🎯 Target", "📝 Content", "🖼️ Images", "📂 Category", "✅ Status"]) # Last is checkbox
        # Configure header resizing
        sched_header = schedule_table.horizontalHeader()
        sched_header.setSectionResizeMode(4, QHeaderView.Stretch) # Content
        sched_header.setSectionResizeMode(7, QHeaderView.Fixed) # Checkbox
        sched_header.resizeSection(7, 30)
        sched_header.setSectionResizeMode(0, QHeaderView.Interactive) # PID
        sched_header.resizeSection(0, 100)
        sched_header.setSectionResizeMode(1, QHeaderView.Interactive) # Date
        sched_header.resizeSection(1, 100)
        sched_header.setSectionResizeMode(2, QHeaderView.Interactive) # Time
        sched_header.resizeSection(2, 80)
        sched_header.setSectionResizeMode(3, QHeaderView.Interactive) # Target
        sched_header.resizeSection(3, 150)
        sched_header.setSectionResizeMode(5, QHeaderView.Interactive) # Images
        sched_header.resizeSection(5, 120)
        sched_header.setSectionResizeMode(6, QHeaderView.Interactive) # Category
        sched_header.resizeSection(6, 100)

        schedule_table.setSelectionMode(QAbstractItemView.NoSelection)
        schedule_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        schedule_table.setAlternatingRowColors(True)
        schedule_table.verticalHeader().setVisible(False)
        tab_layout.addWidget(schedule_table)

        # Add action buttons for schedule management
        schedule_buttons_layout = QHBoxLayout()

        select_all_schedule_btn = QPushButton("Select All")
        deselect_all_schedule_btn = QPushButton("Deselect All")

        # Determine button text and style based on posting mode
        posting_mode = schedule_data[0].get("posting_mode", "automatic") if schedule_data else "automatic"

        if posting_mode == "direct_nst":
            create_scheduled_posts_btn = QPushButton("🚀 Open NST Browser for Direct Posting")
            button_color = "#007bff"  # Blue for direct posting
            button_hover = "#0056b3"
            button_pressed = "#004085"
        else:
            create_scheduled_posts_btn = QPushButton("📅 Create Scheduled Posts")
            button_color = "#28a745"  # Green for automatic posting
            button_hover = "#218838"
            button_pressed = "#1e7e34"

        # Style the create button
        create_scheduled_posts_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {button_color};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {button_hover};
            }}
            QPushButton:pressed {{
                background-color: {button_pressed};
            }}
        """)

        # Add immediate direct posting button for all modes
        direct_post_now_btn = QPushButton("🚀 Post Now via NST Browser")
        direct_post_now_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e8650e;
            }
            QPushButton:pressed {
                background-color: #d15502;
            }
        """)
        direct_post_now_btn.setToolTip("Open NST Browser immediately for selected entries (ignores schedule time)")

        schedule_buttons_layout.addWidget(select_all_schedule_btn)
        schedule_buttons_layout.addWidget(deselect_all_schedule_btn)
        schedule_buttons_layout.addStretch()
        schedule_buttons_layout.addWidget(direct_post_now_btn)
        schedule_buttons_layout.addWidget(create_scheduled_posts_btn)

        tab_layout.addLayout(schedule_buttons_layout)

        # Add tab to the main widget
        index = self.schedulesTabWidget.addTab(tab_widget, QIcon.fromTheme("view-calendar-month"), tab_name)

        # Store data and widgets associated with this tab index
        tab_info = {
            "id": schedule_id,
            "data": schedule_data, # The original full data list
            "table": schedule_table,
            "filters": {
                "time": time_filter_combo,
                "date": date_filter_combo,
                "category": category_filter_combo,
                "target": target_filter_combo,
            },
            "filtered_indices": list(range(len(schedule_data))), # Indices currently shown in table
            "buttons": {
                "select_all": select_all_schedule_btn,
                "deselect_all": deselect_all_schedule_btn,
                "create_scheduled": create_scheduled_posts_btn,
                "direct_post_now": direct_post_now_btn
            }
        }
        self.all_schedules_data[index] = tab_info

        # Connect button signals
        select_all_schedule_btn.clicked.connect(lambda: self._set_all_schedule_rows_checkstate(schedule_table, tab_info["filtered_indices"], True))
        deselect_all_schedule_btn.clicked.connect(lambda: self._set_all_schedule_rows_checkstate(schedule_table, tab_info["filtered_indices"], False))
        create_scheduled_posts_btn.clicked.connect(lambda: self._create_scheduled_posts_from_selected(index))
        direct_post_now_btn.clicked.connect(lambda: self._direct_post_now_from_selected(index))

        # Connect filter signals
        time_filter_combo.currentIndexChanged.connect(lambda: self.apply_schedule_filters(index))
        date_filter_combo.currentIndexChanged.connect(lambda: self.apply_schedule_filters(index))
        category_filter_combo.currentIndexChanged.connect(lambda: self.apply_schedule_filters(index))
        target_filter_combo.currentIndexChanged.connect(lambda: self.apply_schedule_filters(index))

        # Initial population
        self.apply_schedule_filters(index)


    def apply_schedule_filters(self, tab_index):
        """Filters data based on combo boxes and updates the table."""
        if tab_index not in self.all_schedules_data:
             logging.warning(f"Apply filters called for invalid tab index: {tab_index}")
             return

        tab_info = self.all_schedules_data[tab_index]
        full_data = tab_info["data"]
        table = tab_info["table"]
        filters = tab_info["filters"]

        # Get filter values
        time_filter = filters["time"].currentText()
        date_filter = filters["date"].currentText()
        category_filter = filters["category"].currentText()
        target_filter = filters["target"].currentText()

        filtered_indices = []
        for idx, entry in enumerate(full_data):
            # Apply filters - skip entry if it doesn't match
            if time_filter != "All Times" and entry.get("time") != time_filter:
                continue
            if date_filter != "All Dates" and entry.get("date") != date_filter:
                continue
            if category_filter != "All Categories" and entry.get("category") != category_filter:
                continue
            if target_filter != "All Targets" and entry.get("item_name") != target_filter:
                continue

            # If all filters pass, add the original index
            filtered_indices.append(idx)

        # Store the filtered indices
        tab_info["filtered_indices"] = filtered_indices

        # Update the table UI
        table.setUpdatesEnabled(False)
        table.setSortingEnabled(False)
        table.clearContents()
        table.setRowCount(len(filtered_indices))

        category_colors = { # Use consistent colors
            "Recipes": QColor("#FFE0E6"), # Light pink
            "Engage": QColor("#E0FFE0"), # Light green
            "Parole": QColor("#E0F2FF"), # Light blue
        }
        default_color = QColor(Qt.white)

        for row_idx, original_index in enumerate(filtered_indices):
             entry = full_data[original_index]

             # Populate table cells (similar to Posts table, adapt as needed)
             pid_item = QTableWidgetItem(entry.get("post_id", "N/A"))
             pid_item.setTextAlignment(Qt.AlignCenter)
             table.setItem(row_idx, 0, pid_item)

             table.setItem(row_idx, 1, QTableWidgetItem(entry.get("date", "N/A")))
             table.setItem(row_idx, 2, QTableWidgetItem(entry.get("time", "N/A")))
             table.setItem(row_idx, 3, QTableWidgetItem(entry.get("item_name", "N/A")))

             content = entry.get("post_content", "")
             content_display = content[:80] + "..." if len(content) > 80 else content
             content_item = QTableWidgetItem(content_display)
             content_item.setToolTip(content)
             table.setItem(row_idx, 4, content_item)

             # Images (similar to Posts table)
             images_str = entry.get("post_images", "")
             image_paths = [p.strip() for p in images_str.split(',') if p.strip()] if images_str else []
             # (Code for thumbnail widget creation - reuse from Posts table logic)
             # ... create thumb_widget ...
             # table.setCellWidget(row_idx, 5, thumb_widget)
             # For now, just put text:
             img_item = QTableWidgetItem(f"{len(image_paths)} image(s)" if image_paths else "N/A")
             img_item.setTextAlignment(Qt.AlignCenter)
             table.setItem(row_idx, 5, img_item)


             category = entry.get("category", "N/A")
             cat_item = QTableWidgetItem(category)
             cat_item.setTextAlignment(Qt.AlignCenter)
             table.setItem(row_idx, 6, cat_item)

             # Checkbox with status indicator (link state to the original data entry)
             chk = QCheckBox()
             is_checked = entry.get("checked", False)
             has_scheduled_post = "scheduled_post_id" in entry

             chk.setChecked(is_checked)
             # Store original index to modify the correct data entry
             chk.setProperty("original_index", original_index)
             chk.stateChanged.connect(lambda state, idx=original_index: self._schedule_entry_state_changed(tab_index, idx, state))

             # Add status indicator
             status_label = QLabel()
             if has_scheduled_post:
                 status_label.setText("📅")
                 status_label.setToolTip("Scheduled post created")
             elif is_checked:
                 status_label.setText("⏳")
                 status_label.setToolTip("Selected for scheduling")
             else:
                 status_label.setText("")

             chk_container = QWidget()
             chk_layout = QHBoxLayout(chk_container)
             chk_layout.addWidget(chk, alignment=Qt.AlignCenter)
             chk_layout.addWidget(status_label, alignment=Qt.AlignCenter)
             chk_layout.setContentsMargins(0, 0, 0, 0)
             table.setCellWidget(row_idx, 7, chk_container)

             # Apply row color
             row_color = category_colors.get(category, default_color)
             for col in range(table.columnCount()):
                 if table.item(row_idx, col):
                     table.item(row_idx, col).setBackground(row_color)
                 if table.cellWidget(row_idx, col):
                     table.cellWidget(row_idx, col).setStyleSheet(f"background-color: {row_color.name()};")


        table.setSortingEnabled(True)
        table.setUpdatesEnabled(True)
        logging.debug(f"Schedule tab {tab_index} updated with {len(filtered_indices)} rows.")

    def _schedule_entry_state_changed(self, tab_index, original_index, state):
         """Updates the 'checked' state in the underlying data list and creates/removes scheduled posts."""
         if tab_index in self.all_schedules_data:
              if 0 <= original_index < len(self.all_schedules_data[tab_index]["data"]):
                   entry = self.all_schedules_data[tab_index]["data"][original_index]
                   is_checked = (state == Qt.Checked)

                   # Update the checked state
                   entry["checked"] = is_checked

                   # Create or remove scheduled post based on checkbox state
                   if is_checked:
                       self._create_scheduled_post_from_entry(entry, tab_index)
                   else:
                       self._remove_scheduled_post_from_entry(entry)

              else:
                   logging.warning(f"Invalid original_index {original_index} for schedule state change.")
         else:
              logging.warning(f"Invalid tab_index {tab_index} for schedule state change.")

    def _create_scheduled_post_from_entry(self, entry, tab_index):
        """Creates a scheduled post in the database from a schedule entry."""
        try:
            # Parse date and time from entry
            date_str = entry.get("date")  # ISO format: "2024-01-15"
            time_str = entry.get("time")  # Format: "10:30 AM"

            if not date_str or not time_str:
                logging.error(f"Missing date or time in schedule entry: {entry}")
                return False

            # Parse date
            try:
                date_obj = datetime.datetime.fromisoformat(date_str).date()
            except ValueError:
                logging.error(f"Invalid date format in schedule entry: {date_str}")
                return False

            # Parse time
            try:
                time_obj = datetime.datetime.strptime(time_str, "%I:%M %p").time()
            except ValueError:
                logging.error(f"Invalid time format in schedule entry: {time_str}")
                return False

            # Combine date and time
            scheduled_datetime = datetime.datetime.combine(date_obj, time_obj)
            scheduled_timestamp = scheduled_datetime.timestamp()

            # Get account information
            account_info = entry.get("account", {})
            if isinstance(account_info, dict):
                account_id = account_info.get("id")
            else:
                # Handle case where account_info might be an AccountData object
                account_id = getattr(account_info, 'id', None)

            if not account_id:
                logging.error(f"Missing account ID in schedule entry: {entry}")
                logging.error(f"Account info type: {type(account_info)}, value: {account_info}")
                return False

            # Get post content and images
            content = entry.get("post_content", "")
            image_path = entry.get("post_images", "")
            db_post_id = entry.get("db_post_id")
            category = entry.get("category", "")

            # Get target information for posting
            target_info = entry.get("target_info", {})
            target_type = entry.get("target_type", "")

            # Create the scheduled post
            with QMutexLocker(db_mutex):
                cursor = c.execute('''
                    INSERT INTO posts (content, image_path, scheduled_time, account_id, status, post_type, created_at,
                                     target_page_id, target_group_id, original_post_id)
                    VALUES (?, ?, ?, ?, 'pending', ?, CURRENT_TIMESTAMP, ?, ?, ?)
                ''', (
                    content,
                    image_path,
                    scheduled_timestamp,  # Store as timestamp for reliable comparison
                    account_id,
                    category,
                    target_info.get("id") if target_type == "page" else None,
                    target_info.get("id") if target_type == "group" else None,
                    db_post_id  # Reference to original post
                ))
                conn.commit()
                new_scheduled_post_id = cursor.lastrowid

            # Store the scheduled post ID in the entry for later reference
            entry["scheduled_post_id"] = new_scheduled_post_id

            # Update the schedule in database
            self._update_schedule_in_db(tab_index)

            # Refresh the schedule table to show updated status
            self.apply_schedule_filters(tab_index)

            logging.info(f"✅ Created scheduled post ID {new_scheduled_post_id} for {scheduled_datetime}")
            self.statusBar.showMessage(f"✅ تم إنشاء منشور مجدول للتاريخ {date_str} في {time_str}", 3000)

            return True

        except Exception as e:
            logging.error(f"Error creating scheduled post from entry: {e}")
            return False

    def _remove_scheduled_post_from_entry(self, entry):
        """Removes a scheduled post from the database when unchecked."""
        try:
            scheduled_post_id = entry.get("scheduled_post_id")

            if not scheduled_post_id:
                # No scheduled post was created yet, nothing to remove
                return True

            # Remove the scheduled post from database
            with QMutexLocker(db_mutex):
                c.execute("DELETE FROM posts WHERE id = ? AND status = 'pending'", (scheduled_post_id,))
                conn.commit()

            # Remove the reference from entry
            if "scheduled_post_id" in entry:
                del entry["scheduled_post_id"]

            logging.info(f"🗑️ Removed scheduled post ID {scheduled_post_id}")
            self.statusBar.showMessage(f"🗑️ تم إلغاء المنشور المجدول", 2000)

            return True

        except Exception as e:
            logging.error(f"Error removing scheduled post from entry: {e}")
            return False

    def _update_schedule_in_db(self, tab_index):
        """Updates the schedule JSON in the database."""
        try:
            if tab_index not in self.all_schedules_data:
                return False

            tab_info = self.all_schedules_data[tab_index]
            schedule_id = tab_info["id"]
            schedule_data = tab_info["data"]

            # Convert to JSON and update database
            schedule_json = json.dumps(schedule_data)
            with QMutexLocker(db_mutex):
                c.execute("UPDATE schedules SET schedule_json = ? WHERE id = ?", (schedule_json, schedule_id))
                conn.commit()

            return True

        except Exception as e:
            logging.error(f"Error updating schedule in database: {e}")
            return False

    def _create_scheduled_posts_from_selected(self, tab_index):
        """Creates scheduled posts from all selected (checked) entries in the schedule."""
        try:
            if tab_index not in self.all_schedules_data:
                QMessageBox.warning(self, "Error", "Invalid schedule tab.")
                return

            tab_info = self.all_schedules_data[tab_index]
            schedule_data = tab_info["data"]

            # Find all checked entries
            selected_entries = []
            for entry in schedule_data:
                if entry.get("checked", False):
                    selected_entries.append(entry)

            if not selected_entries:
                QMessageBox.information(self, "No Selection", "Please select at least one schedule entry.")
                return

            # Check posting mode of selected entries
            posting_modes = set(entry.get("posting_mode", "automatic") for entry in selected_entries)

            if "direct_nst" in posting_modes:
                # Handle direct NST Browser posting
                self._handle_direct_nst_posting(selected_entries, tab_index)
                return
            else:
                # Handle automatic posting (original behavior)
                # Confirm with user
                reply = QMessageBox.question(
                    self,
                    "Create Scheduled Posts",
                    f"Create {len(selected_entries)} scheduled posts from selected entries?\n\n"
                    "This will create actual posts in the database that will be published at the scheduled times.",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply != QMessageBox.Yes:
                    return

            # Create progress dialog
            progress = QProgressDialog("Creating scheduled posts...", "Cancel", 0, len(selected_entries), self)
            progress.setWindowModality(Qt.WindowModal)
            progress.setWindowTitle("Creating Scheduled Posts")
            progress.setValue(0)

            created_count = 0
            failed_count = 0

            for i, entry in enumerate(selected_entries):
                if progress.wasCanceled():
                    break

                progress.setLabelText(f"Creating post {i+1} of {len(selected_entries)}...")
                progress.setValue(i)
                QtWidgets.QApplication.processEvents()

                # Create scheduled post from entry
                if self._create_scheduled_post_from_entry(entry, tab_index):
                    created_count += 1
                else:
                    failed_count += 1

            progress.setValue(len(selected_entries))
            progress.close()

            # Show results
            if created_count > 0:
                message = f"✅ Successfully created {created_count} scheduled posts!"
                if failed_count > 0:
                    message += f"\n⚠️ {failed_count} posts failed to create."
                QMessageBox.information(self, "Success", message)

                # Refresh posts data to show new scheduled posts
                self.load_posts_data()
                self.update_dashboard()

                # Update status bar
                self.statusBar.showMessage(f"Created {created_count} scheduled posts", 5000)
            else:
                QMessageBox.warning(self, "Failed", f"Failed to create any scheduled posts. {failed_count} entries failed.")

        except Exception as e:
            logging.error(f"Error creating scheduled posts from selected: {e}")
            QMessageBox.critical(self, "Error", f"An error occurred while creating scheduled posts:\n{str(e)}")

    def _handle_direct_nst_posting(self, selected_entries, tab_index):
        """Handle direct NST Browser posting for selected entries."""
        try:
            # Confirm with user
            reply = QMessageBox.question(
                self,
                "Direct NST Browser Posting",
                f"Open NST Browser for direct posting of {len(selected_entries)} selected entries?\n\n"
                "This will open NST Browser for each entry, allowing you to post manually.\n"
                "No posts will be added to the database.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply != QMessageBox.Yes:
                return

            # Create progress dialog
            progress = QProgressDialog("Opening NST Browser for direct posting...", "Cancel", 0, len(selected_entries), self)
            progress.setWindowModality(Qt.WindowModal)
            progress.setWindowTitle("Direct NST Browser Posting")
            progress.setValue(0)

            posted_count = 0
            failed_count = 0

            for i, entry in enumerate(selected_entries):
                if progress.wasCanceled():
                    break

                progress.setLabelText(f"Opening browser for entry {i+1} of {len(selected_entries)}...")
                progress.setValue(i)
                QtWidgets.QApplication.processEvents()

                # Open NST Browser for direct posting
                if self._open_nst_browser_for_entry(entry):
                    posted_count += 1
                    # Mark entry as posted in the schedule
                    entry["checked"] = False  # Uncheck after posting
                    entry["posted_directly"] = True  # Mark as posted directly
                else:
                    failed_count += 1

                # Small delay between browser openings
                time.sleep(1)

            progress.close()

            # Update the schedule table to reflect changes
            self.apply_schedule_filters(tab_index)

            # Show summary
            if posted_count > 0:
                QMessageBox.information(
                    self,
                    "Direct Posting Complete",
                    f"✅ Opened NST Browser for {posted_count} entries.\n"
                    f"❌ Failed to open browser for {failed_count} entries.\n\n"
                    "Posts were made directly in the browser and not added to the database."
                )
            else:
                QMessageBox.warning(
                    self,
                    "Direct Posting Failed",
                    "Failed to open NST Browser for any entries."
                )

        except Exception as e:
            logging.error(f"Error in direct NST posting: {e}")
            QMessageBox.critical(self, "Error", f"An error occurred during direct NST posting: {str(e)}")

    def _open_nst_browser_for_entry(self, entry):
        """Open NST Browser for a specific schedule entry."""
        try:
            # Get account information
            account_info = entry.get("account", {})
            if isinstance(account_info, dict):
                account_data = AccountData(
                    id=account_info.get("id"),
                    fb_id=account_info.get("fb_id", ""),
                    full_name=account_info.get("full_name", "Unknown"),
                    cookie="",  # Not needed for NST Browser
                    status="active",
                    nst_profile_id=account_info.get("nst_profile_id"),
                    browser_type="nst_browser"
                )
            else:
                # Handle case where account_info might be an AccountData object
                account_data = account_info

            if not account_data.nst_profile_id:
                logging.error(f"No NST profile ID found for account {account_data.full_name}")
                return False

            # Get target information
            target_info = entry.get("target_info", {})
            post_content = entry.get("post_content", "")
            post_images = entry.get("post_images", "")

            # Parse images if they exist
            images = []
            if post_images:
                # Handle both single image path and multiple paths
                if isinstance(post_images, str):
                    if post_images.strip():
                        images = [img.strip() for img in post_images.split(',') if img.strip()]
                elif isinstance(post_images, list):
                    images = post_images

            # Open NST Browser for direct posting
            success = self.open_nst_browser_for_direct_posting(account_data, target_info, post_content, images)

            if success:
                logging.info(f"✅ Opened NST Browser for direct posting: {entry.get('item_name', 'Unknown target')}")
            else:
                logging.error(f"❌ Failed to open NST Browser for: {entry.get('item_name', 'Unknown target')}")

            return success

        except Exception as e:
            logging.error(f"Error opening NST Browser for entry: {e}")
            return False

    def _direct_post_now_from_selected(self, tab_index):
        """Open NST Browser immediately for selected entries, ignoring schedule time."""
        try:
            if tab_index not in self.all_schedules_data:
                QMessageBox.warning(self, "Error", "Invalid schedule tab.")
                return

            tab_info = self.all_schedules_data[tab_index]
            schedule_data = tab_info["data"]

            # Find all checked entries
            selected_entries = []
            for entry in schedule_data:
                if entry.get("checked", False):
                    selected_entries.append(entry)

            if not selected_entries:
                QMessageBox.information(self, "No Selection", "Please select at least one schedule entry to post now.")
                return

            # Force direct NST posting for immediate posting
            self._handle_direct_nst_posting(selected_entries, tab_index)

        except Exception as e:
            logging.error(f"Error in direct post now: {e}")
            QMessageBox.critical(self, "Error", f"An error occurred during immediate posting: {str(e)}")

    def remove_schedule_tab(self, index):
        """Handles the request to close a schedule tab."""
        if index not in self.all_schedules_data:
            # Might be the placeholder tab if implementation allows closing it
            if self.schedulesTabWidget.tabText(index) == "No Schedules":
                 # Don't remove placeholder this way
                 return
            logging.warning(f"Attempted to remove non-existent schedule tab index: {index}")
            return

        tab_info = self.all_schedules_data[index]
        schedule_id = tab_info["id"]
        tab_name = self.schedulesTabWidget.tabText(index)

        reply = QMessageBox.question(
            self, "Remove Schedule",
            f"Are you sure you want to remove '{tab_name}' (ID: {schedule_id})?\n"
            "This will delete the schedule permanently from the database.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                with QMutexLocker(db_mutex):
                    c.execute("DELETE FROM schedules WHERE id=?", (schedule_id,))
                    conn.commit()
                logging.info(f"Removed schedule ID {schedule_id} from database.")
                self.statusBar.showMessage(f"Schedule '{tab_name}' removed.", 4000)

                # Remove from internal data and UI
                del self.all_schedules_data[index]
                self.schedulesTabWidget.removeTab(index)

                 # After removing a tab, indices of subsequent tabs change.
                 # Need to rebuild self.all_schedules_data with correct indices.
                new_schedule_data = {}
                for i in range(self.schedulesTabWidget.count()):
                     # Find the original data based on schedule_id stored somewhere reliable,
                     # or update the keys of all_schedules_data. Simpler to reload for now.
                     pass # For now, rely on full reload or careful index management
                # Simplest fix: Reload all schedules after removal
                self.load_schedules_data()


                # Add placeholder if last tab was removed
                if self.schedulesTabWidget.count() == 0:
                     self._add_placeholder_schedule_tab()

                self._update_schedule_button_states() # Update button enable state

            except sqlite3.Error as e:
                logging.error(f"Database error removing schedule ID {schedule_id}: {e}")
                QMessageBox.critical(self, "Database Error", f"Could not remove schedule: {e}")


    def _get_current_schedule_tab_info(self) -> tuple[int, dict | None]:
         """Gets the index and data dict for the currently selected schedule tab."""
         current_index = self.schedulesTabWidget.currentIndex()
         if current_index == -1 or current_index not in self.all_schedules_data:
              return -1, None
         return current_index, self.all_schedules_data[current_index]


    def _update_schedule_button_states(self):
         """Enables/disables schedule action buttons based on current tab."""
         index, tab_info = self._get_current_schedule_tab_info()
         has_valid_tab = (tab_info is not None)

         self.selectAllScheduleBtn.setEnabled(has_valid_tab)
         self.deselectAllScheduleBtn.setEnabled(has_valid_tab)
         self.removeSelectedScheduleRowsBtn.setEnabled(has_valid_tab)


    def select_all_schedule_rows(self):
         """Selects all checkboxes in the current schedule table."""
         index, tab_info = self._get_current_schedule_tab_info()
         if not tab_info: return
         self._set_all_schedule_rows_checkstate(tab_info["table"], tab_info["filtered_indices"], True)

    def deselect_all_schedule_rows(self):
         """Deselects all checkboxes in the current schedule table."""
         index, tab_info = self._get_current_schedule_tab_info()
         if not tab_info: return
         self._set_all_schedule_rows_checkstate(tab_info["table"], tab_info["filtered_indices"], False)

    def _set_all_schedule_rows_checkstate(self, table, filtered_indices, checked):
         """Helper to set check state for all visible rows."""
         table.setUpdatesEnabled(False)
         for row_idx in range(table.rowCount()):
              widget = table.cellWidget(row_idx, 7) # Checkbox column
              if widget:
                   cb = widget.findChild(QCheckBox)
                   if cb:
                        # Update data model directly to avoid triggering signal loop
                        original_index = cb.property("original_index")
                        tab_index = self.schedulesTabWidget.currentIndex()
                        if tab_index in self.all_schedules_data and original_index is not None:
                             self.all_schedules_data[tab_index]["data"][original_index]["checked"] = checked

                        # Update UI checkbox (block signals if necessary)
                        # cb.blockSignals(True)
                        cb.setChecked(checked)
                        # cb.blockSignals(False)
         table.setUpdatesEnabled(True)

    def remove_selected_schedule_rows(self):
        """Removes rows checked in the current schedule table from the data and DB."""
        tab_index, tab_info = self._get_current_schedule_tab_info()
        if not tab_info:
            QMessageBox.warning(self, "Action Failed", "No schedule tab selected.")
            return

        schedule_id = tab_info["id"]
        full_data = tab_info["data"]
        indices_to_remove = [] # Store original indices from full_data

        # Find checked rows based on the data model (not just UI table)
        for i, entry in enumerate(full_data):
            if entry.get("checked", False):
                indices_to_remove.append(i)

        if not indices_to_remove:
            QMessageBox.information(self, "Remove Rows", "No rows selected to remove.")
            return

        reply = QMessageBox.question(
            self, "Confirm Row Removal",
            f"Are you sure you want to remove {len(indices_to_remove)} selected row(s) from this schedule?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Create the new data list excluding the removed items
            new_schedule_data = [entry for i, entry in enumerate(full_data) if i not in indices_to_remove]

            # Update the database
            try:
                new_schedule_json = json.dumps(new_schedule_data)
                with QMutexLocker(db_mutex):
                    c.execute("UPDATE schedules SET schedule_json=? WHERE id=?", (new_schedule_json, schedule_id))
                    conn.commit()
                logging.info(f"Removed {len(indices_to_remove)} rows from schedule ID {schedule_id}.")
                self.statusBar.showMessage(f"{len(indices_to_remove)} rows removed from schedule.", 4000)

                # Update the internal data store for the tab
                tab_info["data"] = new_schedule_data
                # Re-apply filters which will repopulate the table
                self.apply_schedule_filters(tab_index)

            except json.JSONDecodeError as json_err:
                 logging.error(f"Failed to serialize updated schedule data: {json_err}")
                 QMessageBox.critical(self, "Save Error", f"Could not serialize updated schedule data: {json_err}")
            except sqlite3.Error as db_err:
                 logging.error(f"Database error updating schedule ID {schedule_id} after row removal: {db_err}")
                 QMessageBox.critical(self, "Database Error", f"Could not update schedule: {db_err}")
                 # Consider reloading data from DB to revert UI state on error
                 self.load_schedules_data()


    def refresh_all_schedules(self):
         """Refreshes post content/images in all loaded schedules from the DB."""
         logging.info("Refreshing content for all schedules...")
         progress = QProgressDialog("Refreshing schedule contents...", "Cancel", 0, len(self.all_schedules_data), self)
         progress.setWindowModality(Qt.WindowModal)
         progress.setValue(0)

         updated_count = 0
         db_error = False
         post_cache = {} # Cache post details to reduce DB queries

         for idx, tab_info in self.all_schedules_data.items():
             if progress.wasCanceled(): break
             progress.setLabelText(f"Refreshing {self.schedulesTabWidget.tabText(idx)}...")

             schedule_id = tab_info["id"]
             schedule_data = tab_info["data"]
             needs_db_update = False

             for entry in schedule_data:
                 db_post_id = entry.get("db_post_id")
                 if db_post_id is None: continue # Skip entries without linked post ID

                 # Check cache first
                 cached_post = post_cache.get(db_post_id)

                 if cached_post is None:
                     # Not in cache, fetch from DB
                     try:
                         with QMutexLocker(db_mutex):
                             row = c.execute("SELECT content, image_path, status FROM posts WHERE id=?", (db_post_id,)).fetchone()
                         if row:
                              cached_post = {"content": row["content"], "image_path": row["image_path"], "status": row["status"]}
                              post_cache[db_post_id] = cached_post
                         else:
                              # Post deleted from DB? Mark as invalid in schedule?
                              cached_post = {"content": "[Post Deleted]", "image_path": "", "status": "deleted"}
                              post_cache[db_post_id] = cached_post # Cache the deleted state
                     except sqlite3.Error as e:
                          logging.error(f"DB error fetching post {db_post_id} during schedule refresh: {e}")
                          db_error = True
                          break # Stop refreshing this schedule on DB error

                 # Compare and update entry if changed
                 if entry.get("post_content") != cached_post["content"] or entry.get("post_images") != cached_post["image_path"]:
                     entry["post_content"] = cached_post["content"]
                     entry["post_images"] = cached_post["image_path"]
                     entry["post_status"] = cached_post["status"] # Optionally track post status
                     needs_db_update = True
                     updated_count += 1

             # Update schedule JSON in DB if changes were made
             if needs_db_update and not db_error:
                  try:
                      new_schedule_json = json.dumps(schedule_data)
                      with QMutexLocker(db_mutex):
                           c.execute("UPDATE schedules SET schedule_json=? WHERE id=?", (new_schedule_json, schedule_id))
                           conn.commit()
                      logging.debug(f"Updated schedule {schedule_id} in DB after refresh.")
                  except Exception as e:
                       logging.error(f"Error saving refreshed schedule {schedule_id} to DB: {e}")
                       QMessageBox.warning(self, "Save Error", f"Could not save updates for schedule {schedule_id} after refresh.")
                       # Continue refreshing other schedules

             # Update UI for the current tab if it's visible
             if self.schedulesTabWidget.currentIndex() == idx:
                   self.apply_schedule_filters(idx) # Re-apply filters to show refreshed data

             if db_error: break # Stop outer loop if DB error occurred
             progress.setValue(progress.value() + 1)

         progress.close()

         if db_error:
              QMessageBox.critical(self, "Database Error", "A database error occurred while refreshing schedules. Check logs.")
         elif updated_count > 0:
              QMessageBox.information(self, "Refresh Complete", f"Refreshed content for {updated_count} schedule entries across all loaded schedules.")
              # Ensure the currently visible tab is refreshed if it wasn't updated mid-loop
              current_idx = self.schedulesTabWidget.currentIndex()
              if current_idx in self.all_schedules_data:
                  self.apply_schedule_filters(current_idx)
         elif not progress.wasCanceled():
              QMessageBox.information(self, "Refresh Complete", "No schedule content needed updating.")

         self.statusBar.showMessage("Schedule refresh finished.", 4000)


    # --- Pages/Groups Manager Actions ---

    # Fetch functionality removed - data is now added directly during extraction from NST Browser profiles








    def _update_page_group_table(self, table: QTableWidget, data_list: list[dict]):
         """Populates the Pages or Groups table."""
         table.setUpdatesEnabled(False)
         table.setSortingEnabled(False)
         table.clearContents()
         table.setRowCount(len(data_list))

         for row_idx, item_data in enumerate(data_list):
             name = item_data.get("name", "N/A")
             item_id = item_data.get("id", "N/A")
             link = item_data.get("link", "")

             # Checkbox
             cb = QCheckBox()
             cb_container = QWidget()
             cb_layout = QHBoxLayout(cb_container)
             cb_layout.addWidget(cb, alignment=Qt.AlignCenter)
             cb_layout.setContentsMargins(0, 0, 0, 0)
             table.setCellWidget(row_idx, 0, cb_container)

             # Name
             name_item = QTableWidgetItem(name)
             name_item.setToolTip(name)
             table.setItem(row_idx, 1, name_item)

             # ID
             id_item = QTableWidgetItem(item_id)
             table.setItem(row_idx, 2, id_item)

             # Link
             link_item = QTableWidgetItem(link)
             link_item.setToolTip("Double-click to open link")
             table.setItem(row_idx, 3, link_item)
             if not link: # Disable link if empty
                  link_item.setFlags(link_item.flags() & ~Qt.ItemIsEnabled)

         table.setSortingEnabled(True)
         table.setUpdatesEnabled(True)

    def _select_all_table_rows(self, table: QTableWidget, select: bool):
         """Generic helper to select/deselect all checkboxes in column 0 of a table."""
         for row in range(table.rowCount()):
              widget = table.cellWidget(row, 0)
              if widget:
                   cb = widget.findChild(QCheckBox)
                   if cb:
                        cb.setChecked(select)

    def _delete_selected_pages_or_groups(self, is_pages: bool):
        """Delete selected pages or groups from database and UI."""
        table = self.pagesTable if is_pages else self.groupsTable
        table_name = "pages" if is_pages else "groups"
        data_list = self.all_pages_data if is_pages else self.all_groups_data

        # Collect selected items
        selected_ids = []
        selected_names = []

        for row in range(table.rowCount()):
            widget = table.cellWidget(row, 0)
            if widget:
                cb = widget.findChild(QCheckBox)
                if cb and cb.isChecked():
                    # Get the data from the corresponding row
                    if row < len(data_list):
                        item_data = data_list[row]
                        selected_ids.append(item_data['id'])
                        selected_names.append(item_data['name'])

        if not selected_ids:
            QMessageBox.information(self, "Delete Items", f"No {table_name} selected to delete.")
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete {len(selected_ids)} selected {table_name}?\n\n"
            f"Items to delete:\n" + "\n".join(f"• {name}" for name in selected_names[:5]) +
            (f"\n... and {len(selected_names) - 5} more" if len(selected_names) > 5 else ""),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                with QMutexLocker(db_mutex):
                    # Delete from database
                    placeholders = ','.join(['?' for _ in selected_ids])
                    c.execute(f"DELETE FROM {table_name} WHERE id IN ({placeholders})", selected_ids)
                    conn.commit()

                logging.info(f"Deleted {len(selected_ids)} {table_name}: {selected_ids}")
                self.statusBar.showMessage(f"{len(selected_ids)} {table_name} deleted successfully.", 4000)

                # Reload data to refresh UI
                if is_pages:
                    self.load_pages_data()
                else:
                    self.load_groups_data()

            except sqlite3.Error as e:
                logging.error(f"Database error deleting {table_name}: {e}")
                QMessageBox.critical(self, "Database Error", f"Could not delete {table_name}: {e}")

    def open_page_or_group_link(self, item: QTableWidgetItem):
        """Opens the link in column 3 when a cell is double-clicked."""
        if item.column() == 3: # Link column
            link_url = item.text()
            if link_url and link_url.startswith("http"):
                try:
                    QDesktopServices.openUrl(QUrl(link_url))
                except Exception as e:
                    logging.error(f"Failed to open URL {link_url}: {e}")
                    QMessageBox.warning(self, "Open Link Failed", f"Could not open the link:\n{link_url}\n\nError: {e}")
            elif link_url:
                 QMessageBox.warning(self, "Invalid Link", f"The link is not valid:\n{link_url}")


    # --- Background Post Checking ---

    def run_post_check_worker(self):
        """Starts the CheckPostsWorker if not already running."""
        if self.post_check_worker_instance and self.post_check_worker_instance.isRunning():
            logging.debug("CheckPostsWorker is already running.")
            return

        logging.debug("Starting CheckPostsWorker...")
        self.statusBar.showMessage("🔄 جاري فحص المنشورات المجدولة للنشر...", 3000)

        self.post_check_worker_instance = CheckPostsWorker()
        self.post_check_worker_instance.postsStatusChanged.connect(self.update_post_statuses_ui)
        self.post_check_worker_instance.errorOccurred.connect(self._handle_worker_error)
        self.post_check_worker_instance.postPublished.connect(self.on_post_published)
        self.post_check_worker_instance.postingProgress.connect(self.on_posting_progress)
        # Clean up thread object when finished
        self.post_check_worker_instance.finished.connect(self.post_check_worker_instance.deleteLater)
        self.post_check_worker_instance.start()

    def _handle_worker_error(self, error_message):
        """Displays errors reported by worker threads."""
        logging.error(f"Background worker error: {error_message}")
        self.statusBar.showMessage(f"Worker Error: {error_message}", 6000)

    def on_post_published(self, post_id, status):
        """Handle post publication notifications with enhanced UI feedback."""
        if status == "posted":
            logging.info(f"✅ Post {post_id} published successfully")
            self.statusBar.showMessage(f"✅ تم نشر المنشور {post_id} بنجاح", 8000)

            # Show success notification
            try:
                QMessageBox.information(
                    self,
                    "نجح النشر",
                    f"✅ تم نشر المنشور {post_id} بنجاح على فيسبوك!",
                    QMessageBox.Ok
                )
            except:
                pass  # Don't let notification errors break the flow

        elif status == "failed":
            logging.warning(f"❌ Post {post_id} failed to publish")
            self.statusBar.showMessage(f"❌ فشل نشر المنشور {post_id}", 8000)

            # Show error notification
            try:
                QMessageBox.warning(
                    self,
                    "فشل النشر",
                    f"❌ فشل في نشر المنشور {post_id}. يرجى المحاولة مرة أخرى.",
                    QMessageBox.Ok
                )
            except:
                pass  # Don't let notification errors break the flow

        # Refresh posts data to show updated status
        self.load_posts_data()
        # Update dashboard with new statistics
        self.update_dashboard()

    def on_posting_progress(self, message):
        """Handle posting progress messages."""
        logging.info(f"Posting progress: {message}")
        self.statusBar.showMessage(message, 5000)

    # --- Auto-posting Control ---

    def update_auto_post_button_text(self):
        """Update the auto-posting button text and icon based on current state."""
        if self.settings.get('auto_publish', True):
            self.autoPostBtn.setText("🔴 إيقاف النشر التلقائي")
            self.autoPostBtn.setIcon(QIcon.fromTheme("media-playback-stop"))
            self.autoPostBtn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            self.autoPostBtn.setToolTip("النشر التلقائي مفعل - اضغط لإيقافه")
        else:
            self.autoPostBtn.setText("🟢 بدء النشر التلقائي")
            self.autoPostBtn.setIcon(QIcon.fromTheme("media-playback-start"))
            self.autoPostBtn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            self.autoPostBtn.setToolTip("النشر التلقائي متوقف - اضغط لبدء النشر")

    def toggle_auto_posting(self):
        """Toggle automatic posting on/off."""
        current_state = self.settings.get('auto_publish', True)
        new_state = not current_state

        self.settings['auto_publish'] = new_state
        self.save_app_settings()

        if new_state:
            # Start auto-posting
            check_interval = self.settings.get('check_interval', 60) * 1000
            self.post_check_timer.start(check_interval)
            self.run_post_check_worker()  # Run immediately
            self.statusBar.showMessage("تم تفعيل النشر التلقائي", 3000)
            logging.info("Auto-posting enabled by user")
        else:
            # Stop auto-posting
            self.post_check_timer.stop()
            self.statusBar.showMessage("تم إيقاف النشر التلقائي", 3000)
            logging.info("Auto-posting disabled by user")

        self.update_auto_post_button_text()
        self.update_dashboard()  # Update dashboard to reflect new status

    def save_app_settings(self):
        """Save application settings to file."""
        settings_file = "app_settings.json"
        try:
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            logging.debug("Application settings saved")
        except Exception as e:
            logging.error(f"Error saving settings: {e}")

    # --- Reset Functionality ---

    def resetSoftware(self):
        """Resets the application data after confirmation."""
        text, ok = QInputDialog.getText(self, "Confirm Reset",
                                        "This action is irreversible and will delete the database, logs, and account files.\n\n"
                                        "Type 'RESET NOW' (case-sensitive) to confirm:",
                                        QLineEdit.Normal, "")
        if ok and text == "RESET NOW":
            reply = QMessageBox.warning(
                self,
                "Final Confirmation",
                "Are you absolutely sure you want to reset all software data?",
                QMessageBox.Yes | QMessageBox.Cancel,
                QMessageBox.Cancel
            )
            if reply == QMessageBox.Yes:
                logging.warning("Initiating software reset...")
                self.statusBar.showMessage("Resetting software... Application will close.")
                QApplication.processEvents() # Show message

                # 1. Stop Timers and Workers Safely
                self.post_check_timer.stop()
                if self.post_check_worker_instance:
                    self.post_check_worker_instance.stop()
                    # Wait briefly for the worker to finish cleanly if possible
                    self.post_check_worker_instance.wait(2000) # Wait up to 2 seconds

                # 2. Close Database Connection
                # Use a lock to ensure no other thread is using it right before close
                logging.debug("Closing main database connection...")
                locked = db_mutex.tryLock(1000) # Try locking for 1 sec
                try:
                    if locked:
                        if conn:
                            conn.close()
                            logging.info("Main database connection closed.")
                    else:
                        logging.warning("Could not acquire DB mutex to close connection cleanly during reset.")
                finally:
                    if locked: db_mutex.unlock()

                # 3. Shutdown Logging (to release log file handle)
                logging.info("Shutting down logging system...")
                logging.shutdown()

                # 4. Delete Files (Database, Logs, Account JSONs)
                files_to_delete = [DB_FILE, LOG_FILE]
                # Find account files (e.g., account_*.json)
                account_files = glob.glob("account_*.json")
                files_to_delete.extend(account_files)

                deleted_files = []
                failed_files = []
                for f in files_to_delete:
                    try:
                        if os.path.exists(f):
                            os.remove(f)
                            deleted_files.append(os.path.basename(f))
                    except OSError as e:
                        # Log to stderr as logging is shut down
                        print(f"RESET ERROR: Failed to delete file {f}: {e}", file=sys.stderr)
                        failed_files.append(os.path.basename(f))

                # 5. Show Final Message and Quit
                final_message = "Software reset complete.\n"
                if deleted_files:
                    final_message += f"Deleted: {', '.join(deleted_files)}\n"
                if failed_files:
                    final_message += f"Failed to delete: {', '.join(failed_files)}\nCheck permissions.\n"
                final_message += "\nPlease restart the application."

                # Use a simple message box as the main loop might be unstable
                msgBox = QMessageBox(QMessageBox.Information, "Reset Complete", final_message)
                msgBox.exec_()

                # Force exit after reset (safer than relying on normal close)
                os._exit(0) # Use os._exit for immediate termination

        elif ok: # User entered text but it wasn't correct
            QMessageBox.warning(self, "Reset Cancelled", "Incorrect confirmation text. Software reset has been cancelled.")


    # --- Cleanup ---
    def closeEvent(self, event):
        """Handles application closing."""
        logging.info("Close event triggered. Shutting down...")
        self.statusBar.showMessage("Closing application...")

        # Save application settings
        try:
            save_app_settings(self.settings)
            logging.info("Application settings saved")
        except Exception as e:
            logging.error(f"Error saving settings: {e}")

        # Stop timers
        self.post_check_timer.stop()

        # Stop worker threads
        if self.post_check_worker_instance:
            self.post_check_worker_instance.stop()
            # Wait a short time for worker to finish file/db operations
            if not self.post_check_worker_instance.wait(2000): # Wait max 2 secs
                 logging.warning("CheckPostsWorker did not finish cleanly.")

        # Stop NST check worker if running
        if hasattr(self, 'nst_check_all_worker') and self.nst_check_all_worker:
            if self.nst_check_all_worker.isRunning():
                self.check_all_cancelled = True
                self.nst_check_all_worker.terminate()
                if not self.nst_check_all_worker.wait(3000):
                    logging.warning("NST check worker did not finish cleanly.")

        # Clean up any remaining NST Browser processes
        try:
            kill_nstchrome_processes()
            logging.info("NST Browser processes cleaned up")
        except Exception as e:
            logging.warning(f"Error cleaning up NST processes: {e}")

        # Close DB connection (ensure safety with mutex)
        logging.debug("Closing main database connection on exit...")
        locked = db_mutex.tryLock(1000)
        try:
             if locked:
                  if conn:
                       conn.close()
                       logging.info("Main database connection closed.")
             else:
                  logging.warning("Could not acquire DB mutex to close connection on exit.")
        finally:
             if locked: db_mutex.unlock()

        logging.info("Application shutdown complete.")
        event.accept()


# --- Main Execution ---
if __name__ == "__main__":
    # Enable High DPI scaling for better visuals on modern displays
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # Set app details (optional, for window managers)
    # Use QApplication since it inherits from QCoreApplication
    QApplication.setApplicationName("FacebookScheduler")
    QApplication.setOrganizationName("YourOrg") # Replace if applicable

    app = QApplication(sys.argv)

    # Check if database connection was successful during init
    if not conn:
         # Error already logged/printed in initialize_database
         # Optionally show a simple GUI message box here too if needed,
         # but exiting might be sufficient as console shows the critical error.
         sys.exit(1)

    # --- Load FontAwesome or other Icons (Optional) ---
    # ... (icon loading code remains the same)

    window = FacebookSchedulerMain()
    window.show()
    sys.exit(app.exec_())