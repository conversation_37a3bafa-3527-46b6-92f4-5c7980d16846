from PyQt5.QtWidgets import QTableWidget

class PostsTable(QTableWidget):
    post_selected = pyqtSignal(int)  # Signal when a post is selected
    
    def __init__(self):
        super().__init__()
        self.setup_columns()
        self.load_data()
        
    def setup_columns(self):
        self.setColumnCount(5)
        self.setHorizontalHeaderLabels(["ID", "Content", "Type", "Status", "Actions"])
        
    def load_data(self):
        # Load data from database
        pass 